# Knowledge Graph Visualizer - Chat Workflow Architecture

## Overview

This document provides a comprehensive description of the workflows and processes involved in a chat interaction within the 360t-kg knowledge graph visualization system. The system integrates multiple components including PostgreSQL, Graphiti, Ollama, Redis, frontend, proxy, backend, and Neo4j to create a seamless query and response experience.

## System Architecture

The system follows a 5-layer architecture:

1. **Frontend Layer**: React components (ChatView.jsx, ChatContext.jsx) - Port 5177
2. **Proxy Layer**: Express.js session management (proxy-server/) - Port 3003 
3. **API Layer**: Node.js backend (360t-kg-api/) - Port 3002
4. **AI Layer**: FastAPI Python service (main.py) - Port 8000
5. **Database Layer**: Neo4j + Ollama integration

```mermaid
graph TD
    subgraph "Client Layer"
        UI[Web UI - React/TypeScript]
        Mobile[Mobile App - Future]
    end
    
    subgraph "API Gateway"
        Gateway[API Gateway/Load Balancer]
    end
    
    subgraph "Microservices"
        GraphAPI[Graph API Service]
        ChatService[Chat AI Service]
        AuthService[Authentication Service]
        NotificationService[Notification Service]
    end
    
    subgraph "Data Layer"
        Neo4j[(Neo4j Graph Database)]
        Redis[(Redis Cache)]
        S3[File Storage]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Ollama[Ollama LLM]
        Prometheus[Prometheus Metrics]
        Grafana[Grafana Dashboards]
    end
    
    UI --> Gateway
    Gateway --> GraphAPI
    Gateway --> ChatService
    Gateway --> AuthService
    
    GraphAPI --> Neo4j
    GraphAPI --> Redis
    ChatService --> OpenAI
    ChatService --> Ollama
    ChatService --> Neo4j
    
    GraphAPI --> Prometheus
    ChatService --> Prometheus
```

## Chat Workflow Sequence

### 1. COMPREHENSIVE STREAMING ARCHITECTURE ANALYSIS

**CRITICAL ISSUE IDENTIFIED**: Request body transformation disabled in proxy middleware causing 422 validation errors.

```mermaid
sequenceDiagram
    participant User
    participant Frontend[Frontend<br/>ChatView.jsx<br/>chatStore.js<br/>Port 5177]
    participant Proxy[Proxy Server<br/>Express.js<br/>proxyMiddleware.js<br/>Port 3003]
    participant FastAPI[FastAPI Service<br/>main.py<br/>/chat/stream<br/>Port 8000]
    participant Graphiti[Graphiti Engine<br/>Search & Response<br/>Neo4j Integration]
    participant Neo4j[Neo4j Database<br/>Graph Data<br/>Port 7687]

    Note over User,Neo4j: 🔴 CURRENT BROKEN STREAMING FLOW (422 Validation Error)
    
    User->>Frontend: Click "Use Streaming" in chat
    
    Frontend->>Frontend: sendStreamingMessage() called
    Note over Frontend: Payload: {<br/>message: "What is EMS?",<br/>history: [...],<br/>graphitiSettings: {...}<br/>}
    
    Frontend->>Proxy: POST /api/chat/message/stream<br/>❌ Contains 'message' field
    
    Note over Proxy: 🚨 ISSUE: transformRequestBody() EXISTS<br/>but fixRequestBody() DISABLED (line 97)
    
    Proxy->>Proxy: pathRewrite: /api/chat/message/stream → /chat/stream
    Proxy->>FastAPI: Forward request WITH 'message' field<br/>❌ FastAPI expects 'question' field
    
    FastAPI->>FastAPI: ChatRequest validation
    Note over FastAPI: Pydantic schema expects:<br/>'question' field (required)<br/>Received: 'message' field
    
    FastAPI-->>Proxy: ❌ 422 Unprocessable Content<br/>Validation Error
    Proxy-->>Frontend: ❌ 422 Error Response
    Frontend-->>User: ❌ Failed to load resource

    Note over User,Neo4j: 🟢 INTENDED WORKING STREAMING FLOW (With Fix Applied)
    
    User->>Frontend: Click "Use Streaming" in chat
    Frontend->>Frontend: sendStreamingMessage() called (same payload)
    Frontend->>Proxy: POST /api/chat/message/stream<br/>Contains 'message' field
    
    Note over Proxy: ✅ FIX: Re-enable fixRequestBody()<br/>in proxyMiddleware.js line 97
    
    Proxy->>Proxy: transformRequestBody()<br/>message → question
    Note over Proxy: Transformed payload:<br/>{question: "What is EMS?", conversation_history: [...]}
    
    Proxy->>FastAPI: Forward with 'question' field<br/>✅ Matches ChatRequest schema
    
    FastAPI->>FastAPI: ✅ Pydantic validation passes
    FastAPI->>Graphiti: create_search_engine()
    FastAPI->>Graphiti: SearchParameters.from_graphiti_settings()
    
    FastAPI->>Frontend: SSE: {type: 'connected', message: 'Processing...'}
    FastAPI->>Frontend: SSE: {type: 'thinking', message: 'Searching knowledge graph...'}
    
    Graphiti->>Neo4j: MATCH entity queries
    Neo4j-->>Graphiti: Return nodes and relationships
    
    FastAPI->>Frontend: SSE: {type: 'thinking', message: 'Analyzing entities...'}
    
    Graphiti->>Graphiti: search_and_respond() processing
    
    loop Progressive Answer Streaming
        FastAPI->>Frontend: SSE: {type: 'delta', path: ['answer', 'text'], value: 'chunk'}
        Frontend->>Frontend: applySseDeltaThinking() updates STREAM_THINKING_BUFFER
        Frontend->>User: Real-time answer display
    end
    
    FastAPI->>Frontend: SSE: {type: 'meta', path: ['entities'], value: [...]}
    FastAPI->>Frontend: SSE: {type: 'meta', path: ['sources'], value: [...]}
    FastAPI->>Frontend: SSE: {type: 'meta', path: ['badges'], value: [...]}
    
    FastAPI->>Frontend: SSE: {type: 'final', value: {v2.0 structured response}}
    FastAPI->>Frontend: SSE: {type: 'close', timestamp: '...'}
    
    Frontend->>Frontend: Finalize streaming with complete response
    Frontend->>User: ✅ Complete answer with entities and sources
```

### 2. DETAILED COMPONENT BREAKDOWN

```mermaid
graph TD
    subgraph "Frontend Layer (Port 5177)"
        A[ChatView.jsx] --> B[sendStreamingMessage()]
        B --> C[POST /api/chat/message/stream]
        D[applySseDeltaThinking()] --> E[STREAM_THINKING_BUFFER]
        F[SSE EventSource] --> D
    end
    
    subgraph "Proxy Layer (Port 3003)"
        G[Express Route Handler] --> H[createFastAPIProxy()]
        H --> I[transformRequestBody() - DISABLED ❌]
        I --> J[pathRewrite: /api/chat/message/stream → /chat/stream]
        J --> K[Forward to FastAPI]
        
        I2[transformRequestBody() - ENABLED ✅] 
        I2 --> L[message → question conversion]
        L --> J
    end
    
    subgraph "FastAPI Layer (Port 8000)"
        M[/chat/stream endpoint] --> N[ChatRequest validation]
        N --> O[Pydantic schema check]
        O --> P[create_search_engine()]
        P --> Q[SearchParameters.from_graphiti_settings()]
        Q --> R[search_and_respond()]
        R --> S[StreamingResponse with SSE]
    end
    
    subgraph "AI Processing Layer"
        T[Graphiti Search Engine] --> U[Entity Extraction]
        U --> V[Relationship Analysis]
        V --> W[Response Generation]
        W --> X[Progressive Streaming]
    end
    
    subgraph "Database Layer"
        Y[Neo4j Graph Database] --> Z[MATCH Queries]
        Z --> AA[Node/Relationship Data]
    end
    
    C --> G
    K --> M
    
    N -->|422 Error| AB[❌ Validation Failure]
    AB --> AC[Field name mismatch: 'message' vs 'question']
    
    O -->|Success| P
    S --> F
    
    R --> T
    T --> Y
    
    style I fill:#ffcccc,stroke:#ff0000,stroke-width:3px
    style I2 fill:#ccffcc,stroke:#00ff00,stroke-width:3px
    style AB fill:#ffcccc,stroke:#ff0000,stroke-width:3px
    style AC fill:#ffcccc,stroke:#ff0000,stroke-width:3px
```

### 3. SSE EVENT TYPES AND DATA FLOW

```mermaid
graph LR
    subgraph "Server-Sent Events Flow"
        A[connected] --> B[thinking]
        B --> C[delta: thinking content]
        C --> D[delta: answer chunks]
        D --> E[meta: entities]
        E --> F[meta: sources]
        F --> G[meta: badges]
        G --> H[final: v2.0 response]
        H --> I[close: stream end]
    end
    
    subgraph "Frontend Processing"
        J[EventSource.onmessage] --> K[applySseDeltaThinking()]
        K --> L{Event Type?}
        L -->|delta + path=['thinking']| M[STREAM_THINKING_BUFFER.thinking]
        L -->|delta + path=['answer','text']| N[STREAM_THINKING_BUFFER.answerText]
        L -->|meta| O[Metadata arrays update]
        L -->|final| P[Complete response object]
        L -->|close| Q[End streaming state]
    end
    
    subgraph "UI Updates"
        M --> R[Real-time thinking display]
        N --> S[Progressive answer display]
        O --> T[Entities/sources display]
        P --> U[Final structured response]
        Q --> V[Remove streaming indicators]
    end
    
    A --> J
    I --> V
```

### 4. ROOT CAUSE ANALYSIS

**Why We've Been Going Back and Forth:**

1. **Working Architecture Exists**: The complete streaming system is properly designed
2. **Single Point of Failure**: Request transformation disabled in proxy middleware (line 97)
3. **Symptom Chasing**: We tried fixing frontend, FastAPI, and routing instead of the actual issue
4. **Field Name Mismatch**: Frontend sends `message`, FastAPI expects `question`
5. **Simple Fix Available**: Re-enable existing transformRequestBody() function

**The Fix:**
```javascript
// In proxy-server/middleware/proxyMiddleware.js line 97
// CHANGE FROM:
// fixRequestBody(proxyReq, req, res); // TEMPORARILY DISABLED

// CHANGE TO:
fixRequestBody(proxyReq, req, res); // ✅ RE-ENABLED
```

### 5. User Interaction

The chat workflow begins when a user submits a query through the frontend interface:

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Proxy
    participant Backend
    participant AI
    participant Neo4j
    participant Ollama
    
    User->>Frontend: Submit query "What is EMS?"
    Frontend->>Proxy: POST /api/chat
    Proxy->>Backend: Forward request
    Backend->>AI: Forward to FastAPI (port 8000)
```

### 2. Request Processing

The request flows through the system with each component performing specific functions:

#### Frontend to Proxy
- **Component**: 360t-kg-ui/src/components/ChatView.jsx
- **Function**: Captures user input and sends to proxy server
- **Data Flow**: 
  ```javascript
  {
    question: "What is EMS?",
    conversation_history: [...],
    graphitiSettings: {
      searchType: "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
      llmProvider: "ollama",
      ollamaModel: "gemma3:latest",
      edgeCount: 6,
      nodeCount: 2,
      diversityFactor: 0.3
    }
  }
  ```

#### Proxy to Backend API
- **Component**: proxy-server/routes/chatRoutes.js
- **Function**: Routes requests and manages sessions
- **Data Flow**: Transforms and forwards request to backend API

#### Backend API to AI Layer
- **Component**: 360t-kg-api/routes/chatRoutes.js
- **Function**: Processes request and forwards to AI service
- **Data Flow**: 
  ```javascript
  const scriptPath = path.resolve('..', 'graphiti_hybrid_search.py');
  const args = [
    '--search-type', graphitiSettings.searchType,
    '--llm-provider', graphitiSettings.llmProvider,
    '--ollama-model', graphitiSettings.ollamaModel,
    '--edge-count', String(graphitiSettings.edgeCount),
    '--node-count', String(graphitiSettings.nodeCount),
    '--diversity-factor', String(graphitiSettings.diversityFactor)
  ];
  ```

### 3. AI Processing with Graphiti

The AI layer processes the request using the Graphiti search engine:

#### Graphiti Search Engine Workflow

```mermaid
graph TD
    A[User Query] --> B[Query Enhancement]
    B --> C[Domain Knowledge Expansion]
    C --> D[Hybrid Search Execution]
    D --> E[Neo4j Entity Search]
    E --> F[Vector Similarity + BM25]
    F --> G[Result Fusion]
    G --> H[RRF Score Fusion]
    H --> I[Context Building]
    I --> J[LLM Generation]
    J --> K[Ollama API Call]
    K --> L[Response Transformation]
    L --> M[Structured Response]
```

#### Graphiti Search Parameters

| Parameter | Default Value | Description |
|---------|-------------|------------|
| searchType | COMBINED_HYBRID_SEARCH_CROSS_ENCODER | Search algorithm type |
| llmProvider | ollama | LLM provider (ollama, openai, google_genai) |
| ollamaModel | gemma3:latest | Ollama model name for response generation |
| edgeCount | 6 | Number of relationship facts to retrieve |
| nodeCount | 2 | Number of entity summaries to retrieve |
| diversityFactor | 0.3 | MMR diversity factor (0.0=relevance, 1.0=diversity) |
| temperature | 0.3 | LLM temperature (0.0-1.0) |
| timeout | 120 | Request timeout in seconds |

### 4. Database Interactions

#### Neo4j Database Operations

```mermaid
sequenceDiagram
    AI->>Neo4j: MATCH queries for entity search
    Neo4j-->>AI: Return nodes and relationships
    AI->>Neo4j: CALL gds.graph.project for GDS
    Neo4j-->>AI: Return graph projections
    AI->>Neo4j: Pathfinding algorithms
    Neo4j-->>AI: Return relationship paths
```

#### PostgreSQL Chat Storage

```sql
-- Chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. LLM Integration

#### Ollama LLM Processing

```mermaid
sequenceDiagram
    AI->>Ollama: POST /api/generate
    Ollama-->>AI: Streaming response
    AI->>AI: Response parsing and formatting
    AI->>AI: Citation extraction
    AI->>AI: Follow-up question generation
```

#### LLM Configuration

```python
# In graphiti_search_engine.py
class LLMConfiguration:
    # Graphiti internal operations (knowledge graph reasoning)
    graphiti_model: str
    graphiti_url: str
    
    # Final response generation (user-facing answers)
    response_model: str
    response_url: str
    
    # Vector embeddings
    embedding_model: str
    embedding_dimensions: int
    openai_api_key: str
```

### 6. Response Generation

#### Response Builder Process

```mermaid
graph TD
    A[Raw LLM Response] --> B[Answer Extraction]
    B --> C[Explanation Parsing]
    C --> D[Follow-up Questions]
    D --> E[Citation Matching]
    E --> F[Structured JSON]
    F --> G[Frontend-Compatible Format]
```

#### Response Structure

```json
{
  "response": {
    "role": "assistant",
    "content": "Answer with markdown formatting",
    "timestamp": "2025-07-14T10:35:00Z",
    "sourceDocuments": [...],
    "sourceNodes": [...]
  },
  "updatedHistory": [...],
  "structured_response": {
    "answer": "Formatted answer",
    "explanation": "Detailed explanation",
    "follow_up_questions": [...],
    "citations": [...],
    "metadata": {
      "search_type": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
      "search_time_ms": 150,
      "response_time_ms": 2000,
      "total_time_ms": 2150
    }
  }
}
```

### 7. Caching with Redis

#### Redis Caching Strategy

```mermaid
sequenceDiagram
    AI->>Redis: Check cache for query hash
    Redis-->>AI: Return cached response (if exists)
    AI->>Neo4j: Query database (if cache miss)
    Neo4j-->>AI: Return results
    AI->>Redis: Store response with TTL
```

#### Cache Configuration

```javascript
// In 360t-kg-api/redis/config.js
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  cacheTTL: 300 // 5 minutes
};
```

## Component Collaboration Flow

### Complete End-to-End Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Proxy
    participant Backend
    participant AI
    participant Neo4j
    participant Ollama
    participant Redis
    participant PostgreSQL
    
    User->>Frontend: Submit query
    Frontend->>Proxy: Send request
    Proxy->>Backend: Forward request
    Backend->>AI: Forward to FastAPI
    AI->>Redis: Check cache
    Redis-->>AI: Cache miss
    AI->>Neo4j: Entity search
    Neo4j-->>AI: Return nodes/relationships
    AI->>Ollama: Generate response
    Ollama-->>AI: Return LLM response
    AI->>PostgreSQL: Save conversation
    AI-->>Backend: Return structured response
    Backend-->>Proxy: Forward response
    Proxy-->>Frontend: Return to UI
    Frontend-->>User: Display response
```

## Performance Metrics

### Response Time Breakdown

| Component | Average Time | Notes |
|---------|-------------|------|
| Frontend Processing | 50ms | Input handling and UI updates |
| Proxy Routing | 20ms | Request forwarding |
| Backend Processing | 30ms | Request validation and routing |
| AI Processing | 150ms | Graphiti initialization |
| Neo4j Query | 80ms | Entity and relationship search |
| Ollama Generation | 2000ms | LLM response generation |
| Response Formatting | 100ms | Structured response creation |
| **Total** | **2430ms** | |

### Error Handling and Fallbacks

```mermaid
graph TD
    A[Request] --> B{Success?}
    B -->|Yes| C[Return response]
    B -->|No| D{Error Type}
    D -->|Timeout| E[Return partial results]
    D -->|LLM Error| F[Fallback to cached response]
    D -->|Database Error| G[Return error message]
    D -->|Validation Error| H[Return input guidance]
```

## Configuration and Settings

### Frontend Settings Service

```javascript
// 360t-kg-ui/src/services/settingsService.js
const settings = {
  graphiti: {
    searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
    diversityFactor: 0.3,
    edgeCount: 6,
    nodeCount: 2,
    llmProvider: 'ollama',
    ollamaModel: 'gemma3:latest',
    ollamaUrl: 'http://localhost:11434',
    temperature: 0.3,
    timeout: 180,
    enableCaching: true,
    logLevel: 'info'
  }
};
```

### Environment Variables

```bash
# In .env file
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=1979@rabu
OLLAMA_URL=http://localhost:11434
OPENAI_API_KEY=sk-...
REDIS_HOST=localhost
REDIS_PORT=6379
```

## Health Checks and Monitoring

### Health Check Endpoints

```mermaid
graph TD
    A[Health Check] --> B{Service}
    B -->|API| C[Check server status]
    B -->|Neo4j| D[Check connectivity]
    B -->|Ollama| E[Check model availability]
    B -->|Redis| F[Check cache connectivity]
    B -->|PostgreSQL| G[Check database]
    C --> H[Return status]
    D --> H
    E --> H
    F --> H
    G --> H
```

### Health Check Response

```json
{
  "status": "healthy",
  "timestamp": "2025-07-14T10:35:00Z",
  "version": "1.0.0",
  "services": {
    "api": "healthy",
    "neo4j": "healthy",
    "ollama": "healthy",
    "redis": "healthy",
    "postgresql": "healthy"
  },
  "uptime": 3600
}
```

## Conclusion

The 360t-kg knowledge graph visualization system demonstrates a sophisticated integration of multiple technologies to provide a seamless chat experience. The workflow involves coordinated efforts between frontend, proxy, backend, AI, and database components, with each playing a crucial role in processing user queries and generating informative responses.

Key architectural decisions include:
- Separation of concerns between different system layers
- Use of Graphiti for advanced hybrid search capabilities
- Integration of multiple LLM providers with fallback mechanisms
- Comprehensive caching strategy using Redis
- Persistent chat storage using PostgreSQL
- Real-time monitoring and health checks

This architecture enables the system to handle complex queries, provide accurate responses with proper citations, and maintain high availability and performance.