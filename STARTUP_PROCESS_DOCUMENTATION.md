# Knowledge Graph Visualizer - Comprehensive Startup Process Documentation

## 🎯 Executive Summary

This document provides the definitive guide to the Knowledge Graph Visualizer's multi-service startup process, including root cause analysis, service architecture, dependency management, and comprehensive troubleshooting strategies for the 5-layer system architecture.

## 🔍 Root Cause Analysis & Problem Resolution

### The Critical Issue Discovered
The frontend was experiencing **504 Gateway Timeout errors** during graph data loading, revealing a fundamental gap in the service startup sequence.

#### Problem Breakdown
```
┌────────────────────────────────────────────────────────────────────────┐
│ ERROR FLOW ANALYSIS                                                    │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ Frontend Request (5177)                                               │
│        ↓                                                               │
│ GET /api/graph/minimal                                                │
│        ↓                                                               │
│ Proxy Server (3003) - Routes to Node.js API (3002)                   │
│        ↓                                                               │
│ ❌ CONNECTION REFUSED - Service Not Running                           │
│        ↓                                                               │
│ 504 Gateway Timeout Error                                             │
│        ↓                                                               │
│ Frontend displays: "Failed to load graph data"                       │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

#### Expected vs Missing Endpoints
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Frontend Expectation│ Required Service    │ Status Before Fix   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ /api/graph/minimal  │ Node.js API (3002)  │ ❌ Service Missing  │
│ /api/graph/initial  │ Node.js API (3002)  │ ❌ Service Missing  │
│ /api/graph/visual   │ Node.js API (3002)  │ ❌ Service Missing  │
│ /api/analysis/*     │ Node.js API (3002)  │ ❌ Service Missing  │
│ /api/chat           │ FastAPI (8000)      │ ✅ Service Running  │
│ /api/conversations  │ FastAPI (8000)      │ ✅ Service Running  │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### The Comprehensive Solution
Updated startup orchestration to include all four critical services with proper dependency management and health validation.

## 🏛️ Complete System Architecture

### 5-Layer Service Architecture
```
┌───────────────────────────────────────────────────────────────────────────┐
│ KNOWLEDGE GRAPH VISUALIZER - 5-LAYER ARCHITECTURE                        │
├───────────────────────────────────────────────────────────────────────────┤
│                                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ LAYER 1: Frontend (React + Vite)                   Port: 5177      │   │
│ │ ├─ React Components (GraphView, ChatView, etc.)                    │   │
│ │ ├─ D3.js Force Simulation (2D Visualization)                       │   │
│ │ ├─ Three.js WebGL Rendering (3D Visualization)                     │   │
│ │ ├─ Performance Monitoring & Error Boundaries                       │   │
│ │ └─ HTTP Client (axios) → Proxy Server                              │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                        HTTP Requests (CORS-enabled)                       │
│                                    │                                       │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ LAYER 2: Proxy Server (Express.js)                 Port: 3003      │   │
│ │ ├─ Session Management (express-session + memory store)             │   │
│ │ ├─ Request Routing & Load Balancing                                │   │
│ │ ├─ CORS Handling & Security Middleware                             │   │
│ │ ├─ Rate Limiting & Request Timing                                  │   │
│ │ └─ Health Monitoring & Service Discovery                           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                    │                               │                       │
│        Graph/Analysis Requests          Chat/AI Requests                  │
│                    │                               │                       │
│ ┌──────────────────┴────────────┐   ┌──────────────┴──────────────────┐   │
│ │ LAYER 3A: API Backend         │   │ LAYER 3B: AI Layer             │   │
│ │ Port: 3002                    │   │ Port: 8000                      │   │
│ │ ├─ Express.js RESTful API     │   │ ├─ FastAPI Async Framework      │   │
│ │ ├─ Neo4j Driver & Queries     │   │ ├─ LLM Provider Abstraction     │   │
│ │ ├─ GDS Algorithm Integration  │   │ ├─ Multi-Provider Failover      │   │
│ │ ├─ Data Transformation        │   │ ├─ Context Window Management     │   │
│ │ └─ GraphRepository Pattern    │   │ └─ Conversation Persistence     │   │
│ └───────────────────────────────┘   └─────────────────────────────────┘   │
│                    │                               │                       │
│                    │                               │                       │
│ ┌──────────────────┴───────────────────────────────┴───────────────────┐   │
│ │ LAYER 4: Database Layer (Neo4j)                    Port: 7687       │   │
│ │ ├─ Graph Database Storage (Nodes, Relationships, Properties)        │   │
│ │ ├─ GDS Library (Node2Vec, Link Prediction Algorithms)              │   │
│ │ ├─ Cypher Query Engine & Optimization                              │   │
│ │ ├─ Index Management & Performance Tuning                           │   │
│ │ └─ Backup & Recovery Systems                                        │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

### Service Dependency Chain
```
┌────────────────────────────────────────────────────────────────────────┐
│ STARTUP SEQUENCE - DEPENDENCY-AWARE SERVICE ORCHESTRATION             │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ Phase 1: Foundation Layer                                              │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ 1. Neo4j Database (External Service)               Port: 7687   │   │
│ │    ├─ Verify connectivity and authentication                    │   │
│ │    ├─ Validate schema and indexes                               │   │
│ │    ├─ Confirm GDS library availability                          │   │
│ │    └─ Test basic queries and performance                        │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                   │
│                            Database Ready                              │
│                                    │                                   │
│ Phase 2: Backend Services                                              │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ 2. FastAPI AI Layer                                Port: 8000   │   │
│ │    ├─ Initialize LLM provider connections                       │   │
│ │    ├─ Test provider health and availability                     │   │
│ │    ├─ Load conversation history and context                     │   │
│ │    └─ Validate chat endpoint functionality                      │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                   │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ 3. Node.js API Backend                             Port: 3002   │   │
│ │    ├─ Establish Neo4j driver connection                         │   │
│ │    ├─ Initialize GraphRepository and services                   │   │
│ │    ├─ Validate graph endpoints and data transformation          │   │
│ │    └─ Test GDS algorithm availability                           │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                   │
│                         Backend Services Ready                         │
│                                    │                                   │
│ Phase 3: Service Orchestration                                         │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ 4. Proxy Server                                    Port: 3003   │   │
│ │    ├─ Configure session management and CORS                     │   │
│ │    ├─ Establish routing to backend services                     │   │
│ │    ├─ Initialize rate limiting and security                     │   │
│ │    └─ Validate endpoint forwarding and health checks            │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                   │
│                            Routing Layer Ready                         │
│                                    │                                   │
│ Phase 4: Frontend Application                                          │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ 5. Frontend Application (React + Vite)            Port: 5177    │   │
│ │    ├─ Build and serve static assets                             │   │
│ │    ├─ Initialize React application and routing                  │   │
│ │    ├─ Test API connectivity through proxy                       │   │
│ │    └─ Validate graph visualization and chat functionality       │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

### Critical Request Flow Patterns

#### Graph Data Requests
```
┌────────────────────────────────────────────────────────────────────────┐
│ GRAPH DATA REQUEST FLOW                                                │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ Frontend (React Component)                                             │
│        ↓                                                               │
│ GraphView.jsx → graphApiService.js                                    │
│        ↓                                                               │
│ axios.get('http://localhost:3003/api/graph/minimal')                  │
│        ↓                                                               │
│ Proxy Server (port 3003) - Session & CORS handling                   │
│        ↓                                                               │
│ Route to: http://localhost:3002/api/graph/minimal                     │
│        ↓                                                               │
│ Node.js API Backend (port 3002)                                      │
│        ↓                                                               │
│ GraphRepository.js → Neo4j Query                                      │
│        ↓                                                               │
│ MATCH (n)-[r]->(m) RETURN n, r, m LIMIT 1000                        │
│        ↓                                                               │
│ Data Transformation & Response                                         │
│        ↓                                                               │
│ JSON Response → Proxy → Frontend                                      │
│        ↓                                                               │
│ D3.js Force Simulation Rendering                                      │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

#### AI Chat Requests
```
┌────────────────────────────────────────────────────────────────────────┐
│ AI CHAT REQUEST FLOW                                                   │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ Frontend (ChatView.jsx)                                               │
│        ↓                                                               │
│ chatApiService.js → User Message + Context                           │
│        ↓                                                               │
│ axios.post('http://localhost:3003/api/chat')                         │
│        ↓                                                               │
│ Proxy Server (port 3003) - Session Management                        │
│        ↓                                                               │
│ Route to: http://localhost:8000/chat                                  │
│        ↓                                                               │
│ FastAPI AI Layer (port 8000)                                         │
│        ↓                                                               │
│ LLM Manager → Provider Selection (OpenAI/Gemini/Ollama)              │
│        ↓                                                               │
│ Context Building → Neo4j Query for Relevant Entities                 │
│        ↓                                                               │
│ LLM Processing → Structured Response Generation                       │
│        ↓                                                               │
│ Enhanced Response with Citations → Proxy → Frontend                   │
│        ↓                                                               │
│ StructuredResponse.jsx Rendering                                      │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

## 🚀 Enhanced Startup Script Features

### Intelligent Startup Orchestration
```
┌────────────────────────────────────────────────────────────────────────┐
│ STARTUP SCRIPT CAPABILITIES                                            │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ ✅ Dependency-Aware Service Sequencing                               │
│    └─ Services start only after dependencies are confirmed healthy    │
│                                                                        │
│ ✅ Comprehensive Health Validation                                    │
│    └─ HTTP health checks + functional endpoint testing                │
│                                                                        │
│ ✅ Advanced Error Handling & Recovery                                 │
│    └─ Retry logic, graceful degradation, detailed error reporting     │
│                                                                        │
│ ✅ Process Lifecycle Management                                       │
│    └─ Clean startup, monitoring, and shutdown of all services         │
│                                                                        │
│ ✅ Real-Time Status Reporting                                         │
│    └─ Colored output, progress indicators, final status summary       │
│                                                                        │
│ ✅ Development vs Production Modes                                     │
│    └─ Environment-specific configurations and optimizations           │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

### Enhanced Usage Commands
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development Startup Commands                                    │
# └─────────────────────────────────────────────────────────────────┘
# Standard multi-service startup (recommended)
npm run dev                          # Uses scripts/simple-dev.js

# Advanced orchestration with monitoring
npm run dev-complex                  # Uses scripts/dev.js

# Service discovery and health checks
npm run list                         # List all services and status

# Legacy shell script (manual control)
./start-services.sh                  # Direct shell script execution

# ┌─────────────────────────────────────────────────────────────────┐
# │ Validation and Testing Commands                                 │
# └─────────────────────────────────────────────────────────────────┘
# Comprehensive service validation
./test-startup-services.sh          # Full health check suite

# Individual service health checks
curl http://localhost:8000/health    # FastAPI health status
curl http://localhost:3002/health    # Node.js API health status  
curl http://localhost:3003/health    # Proxy server health status
curl http://localhost:5177/          # Frontend availability

# ┌─────────────────────────────────────────────────────────────────┐
# │ Debugging and Troubleshooting Commands                         │
# └─────────────────────────────────────────────────────────────────┘
# Port conflict resolution
npx kill-port 8000 && npx kill-port 3002 && npx kill-port 3003 && npx kill-port 5177

# Service log monitoring
tail -f 360t-kg-api/logs/server.log      # API backend logs
tail -f proxy-server/logs/proxy.log      # Proxy server logs  
tail -f 360t-kg-ui/logs/vite.log         # Frontend build logs
```

## 🔬 Comprehensive Service Health Monitoring

### Service Health Check Matrix
```
┌─────────────────────┬─────────────────────┬─────────────────────┬─────────────────────┐
│ Service Name        │ Port │ Health URL     │ Dependencies       │ Critical Endpoints  │
├─────────────────────┼──────┼─────────────────┼─────────────────────┼─────────────────────┤
│ FastAPI AI Layer    │ 8000 │ /health        │ Neo4j, LLM APIs    │ /chat               │
│                     │      │                │ OpenAI, Gemini     │ /conversations      │
│                     │      │                │ Ollama (optional)   │ /health             │
├─────────────────────┼──────┼─────────────────┼─────────────────────┼─────────────────────┤
│ Node.js API Backend │ 3002 │ /health        │ Neo4j Database      │ /api/graph/*        │
│                     │      │                │ GDS Library         │ /api/analysis/*     │
│                     │      │                │                     │ /api/settings       │
├─────────────────────┼──────┼─────────────────┼─────────────────────┼─────────────────────┤
│ Proxy Server        │ 3003 │ /health        │ FastAPI, Node.js    │ /api/* (routing)    │
│                     │      │                │ Session Store       │ /health             │
├─────────────────────┼──────┼─────────────────┼─────────────────────┼─────────────────────┤
│ Frontend App        │ 5177 │ /              │ Proxy Server        │ / (React SPA)       │
│                     │      │                │ Static Assets       │ /assets/*           │
│                     │      │                │                     │ /manifest.json      │
├─────────────────────┼──────┼─────────────────┼─────────────────────┼─────────────────────┤
│ Neo4j Database      │ 7687 │ N/A (bolt)     │ None (external)     │ Cypher queries      │
│                     │      │                │                     │ GDS algorithms      │
└─────────────────────┴──────┴─────────────────┴─────────────────────┴─────────────────────┘
```

### Advanced Health Check Implementation
```javascript
// Enhanced health check with dependency validation
const healthCheck = {
  async validateServiceHealth(serviceName, port, dependencies = []) {
    const startTime = Date.now();
    
    try {
      // 1. Basic connectivity test
      const response = await axios.get(`http://localhost:${port}/health`, {
        timeout: 5000
      });
      
      // 2. Dependency health validation
      for (const dep of dependencies) {
        await this.validateDependency(dep);
      }
      
      // 3. Functional endpoint testing
      await this.validateCriticalEndpoints(serviceName, port);
      
      // 4. Performance metrics collection
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        service: serviceName,
        port: port,
        responseTime: responseTime,
        dependencies: dependencies.map(d => ({ name: d, status: 'healthy' })),
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        status: 'unhealthy',
        service: serviceName,
        port: port,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
};
```

## 🛠️ Comprehensive Troubleshooting Guide

### Service Failure Diagnostic Matrix
```
┌────────────────────────────────────────────────────────────────────────┐
│ SERVICE FAILURE DIAGNOSTIC MATRIX                                      │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ Problem: 504 Gateway Timeout                                           │
│ ├─ Root Cause: Node.js API service not running                        │
│ ├─ Symptoms: Frontend blank, graph data fails to load                 │
│ ├─ Solution: Start Node.js API service (port 3002)                    │
│ └─ Prevention: Use dependency-aware startup script                     │
│                                                                        │
│ Problem: Frontend Shows White Screen                                   │
│ ├─ Root Cause: Services started out of order                          │
│ ├─ Symptoms: No errors in console, React app doesn't initialize       │
│ ├─ Solution: Restart services in correct dependency order             │
│ └─ Prevention: Use npm run dev or enhanced startup script             │
│                                                                        │
│ Problem: Graph Visualization Not Loading                               │
│ ├─ Root Cause: Proxy can't reach Node.js API backend                  │
│ ├─ Symptoms: Network tab shows 504 errors for /api/graph/*            │
│ ├─ Solution: Verify Node.js API health and restart if needed          │
│ └─ Prevention: Monitor service health continuously                     │
│                                                                        │
│ Problem: Chat Functionality Not Working                                │
│ ├─ Root Cause: FastAPI service not running or LLM providers failing   │
│ ├─ Symptoms: Chat messages fail to send, no AI responses              │
│ ├─ Solution: Check FastAPI health and LLM provider API keys           │
│ └─ Prevention: Implement provider failover and health monitoring      │
│                                                                        │
│ Problem: Neo4j Connection Errors                                       │
│ ├─ Root Cause: Database not running or incorrect credentials          │
│ ├─ Symptoms: Backend services fail to start, graph queries timeout    │
│ ├─ Solution: Start Neo4j and verify credentials in .env files         │
│ └─ Prevention: Add Neo4j to service startup validation                │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

### Port Conflict Resolution Strategy
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Port Conflict Detection and Resolution                          │
# └─────────────────────────────────────────────────────────────────┘

# Check for port conflicts across all services
lsof -i :8000 -i :3002 -i :3003 -i :5177 -i :7687

# Kill specific processes by port
npx kill-port 8000    # FastAPI
npx kill-port 3002    # Node.js API  
npx kill-port 3003    # Proxy Server
npx kill-port 5177    # Frontend
npx kill-port 7687    # Neo4j (if running locally)

# Alternative port detection method
netstat -tulpn | grep -E ':(8000|3002|3003|5177|7687)'

# Process identification and cleanup
ps aux | grep -E '(node|python|java)' | grep -E '(8000|3002|3003|5177)'

# ┌─────────────────────────────────────────────────────────────────┐
# │ Service-Specific Troubleshooting                               │
# └─────────────────────────────────────────────────────────────────┘

# FastAPI Service Debugging
python main.py --log-level debug --host 0.0.0.0 --port 8000

# Node.js API Service Debugging  
cd 360t-kg-api && DEBUG=* npm start

# Proxy Server Debugging
cd proxy-server && NODE_ENV=development npm start

# Frontend Development Debugging
cd 360t-kg-ui && npm run dev -- --debug --port 5177
```

### Environment Configuration Validation
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Environment Variable Validation Script                         │
# └─────────────────────────────────────────────────────────────────┘

#!/bin/bash
# validate-env.sh - Comprehensive environment validation

echo "🔍 Validating Environment Configuration..."

# Neo4j Configuration
if [ -z "$NEO4J_URI" ]; then
    echo "❌ NEO4J_URI not set"
    exit 1
fi

if [ -z "$NEO4J_USER" ] || [ -z "$NEO4J_PASSWORD" ]; then
    echo "❌ Neo4j credentials not configured"
    exit 1
fi

# LLM Provider Configuration
if [ -z "$OPENAI_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ] && [ -z "$OLLAMA_URL" ]; then
    echo "⚠️  No LLM providers configured - chat functionality will be limited"
fi

# Proxy Configuration
if [ -z "$SESSION_SECRET" ]; then
    echo "⚠️  SESSION_SECRET not set - using default (not secure for production)"
fi

echo "✅ Environment validation completed"
```

## 📊 Performance Monitoring & Optimization

### Service Performance Metrics
```
┌────────────────────────────────────────────────────────────────────────┐
│ SERVICE PERFORMANCE BENCHMARKS                                         │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ ┌─────────────────────┬─────────────────────┬─────────────────────┐    │
│ │ Service             │ Startup Time        │ Memory Usage        │    │
│ ├─────────────────────┼─────────────────────┼─────────────────────┤    │
│ │ FastAPI (8000)      │ 3-5 seconds         │ 150-200 MB          │    │
│ │ Node.js API (3002)  │ 2-4 seconds         │ 100-150 MB          │    │
│ │ Proxy Server (3003) │ 1-2 seconds         │ 50-75 MB            │    │
│ │ Frontend (5177)     │ 5-10 seconds        │ 200-300 MB (browser)│    │
│ │ Neo4j (7687)        │ 10-30 seconds       │ 512MB-2GB           │    │
│ └─────────────────────┴─────────────────────┴─────────────────────┘    │
│                                                                        │
│ ┌─────────────────────┬─────────────────────┬─────────────────────┐    │
│ │ Endpoint            │ Response Time       │ Throughput          │    │
│ ├─────────────────────┼─────────────────────┼─────────────────────┤    │
│ │ /api/graph/minimal  │ 100-500ms           │ 50 req/sec          │    │
│ │ /api/graph/initial  │ 500-2000ms          │ 10 req/sec          │    │
│ │ /api/chat           │ 3-30 seconds        │ 1-5 req/sec         │    │
│ │ /health endpoints   │ 10-50ms             │ 100+ req/sec        │    │
│ └─────────────────────┴─────────────────────┴─────────────────────┘    │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

### Resource Optimization Strategies
```javascript
// Service optimization configurations
const optimizationConfig = {
  // FastAPI AI Layer optimizations
  fastapi: {
    workers: process.env.NODE_ENV === 'production' ? 4 : 1,
    maxRequestSize: '10MB',
    timeoutSettings: {
      chatTimeout: 60000,        // 60 seconds for LLM responses
      healthTimeout: 5000,       // 5 seconds for health checks
      contextTimeout: 10000      // 10 seconds for context building
    },
    caching: {
      conversationCache: '1h',   // Cache conversation history
      contextCache: '30m'        // Cache context data
    }
  },

  // Node.js API Backend optimizations
  nodeApi: {
    connectionPool: {
      max: 50,                   // Max Neo4j connections
      idleTimeout: 30000,        // Connection idle timeout
      acquisitionTimeout: 60000   // Connection acquisition timeout
    },
    queryOptimization: {
      resultLimit: 1000,         // Max nodes per query
      timeoutMs: 30000,          // Query timeout
      enableExplain: false        // Disable query profiling in production
    }
  },

  // Proxy Server optimizations
  proxy: {
    session: {
      maxAge: 3600000,           // 1 hour session lifetime
      checkPeriod: 900000,       // 15 minute cleanup interval
      store: 'memory'            // Use Redis in production
    },
    rateLimiting: {
      windowMs: 900000,          // 15 minute window
      max: 1000,                 // Max requests per window
      skipHealthChecks: true      // Skip rate limiting for health checks
    }
  }
};
```

## 🔄 Continuous Integration & Deployment

### CI/CD Pipeline Integration
```yaml
# .github/workflows/startup-validation.yml
name: Startup Process Validation

on: [push, pull_request]

jobs:
  validate-services:
    runs-on: ubuntu-latest
    
    services:
      neo4j:
        image: neo4j:5.0
        env:
          NEO4J_AUTH: neo4j/testpassword
        ports:
          - 7687:7687
        options: >-
          --health-cmd "cypher-shell -u neo4j -p testpassword 'RETURN 1'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          npm install
          cd 360t-kg-api && npm install
          cd ../360t-kg-ui && npm install  
          cd ../proxy-server && npm install
          pip install -r requirements.txt

      - name: Validate startup process
        env:
          NEO4J_URI: bolt://localhost:7687
          NEO4J_USER: neo4j
          NEO4J_PASSWORD: testpassword
        run: |
          chmod +x ./start-services.sh
          chmod +x ./test-startup-services.sh
          ./start-services.sh &
          sleep 30
          ./test-startup-services.sh
```

### Docker Compose Orchestration
```yaml
# docker-compose.yml - Production-ready multi-service orchestration
version: '3.8'

services:
  neo4j:
    image: neo4j:5.0-community
    ports:
      - "7687:7687"
      - "7474:7474"
    environment:
      NEO4J_AUTH: neo4j/production_password
      NEO4J_PLUGINS: '["graph-data-science"]'
    volumes:
      - neo4j_data:/data
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "production_password", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  fastapi:
    build: .
    ports:
      - "8000:8000"
    environment:
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: production_password
    depends_on:
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-backend:
    build: ./360t-kg-api
    ports:
      - "3002:3002"
    environment:
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: production_password
    depends_on:
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  proxy:
    build: ./proxy-server
    ports:
      - "3003:3003"
    environment:
      FASTAPI_URL: http://fastapi:8000
      BACKEND_URL: http://api-backend:3002
    depends_on:
      - fastapi
      - api-backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./360t-kg-ui
    ports:
      - "5177:5177"
    environment:
      VITE_API_URL: http://proxy:3003/api
    depends_on:
      proxy:
        condition: service_healthy

volumes:
  neo4j_data:
```

## 📋 Service Validation Checklist

### Pre-Startup Validation
```
┌────────────────────────────────────────────────────────────────────────┐
│ PRE-STARTUP ENVIRONMENT CHECKLIST                                      │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ ☐ Neo4j Database Prerequisites                                        │
│   ☐ Neo4j server running and accessible                              │
│   ☐ Valid credentials configured in .env                             │
│   ☐ GDS library installed and functional                             │
│   ☐ Database schema and indexes validated                            │
│                                                                        │
│ ☐ LLM Provider Configuration                                          │
│   ☐ At least one LLM provider API key configured                     │
│   ☐ Provider endpoints accessible and responsive                     │
│   ☐ Rate limits and quotas sufficient for usage                      │
│                                                                        │
│ ☐ Development Environment                                              │
│   ☐ Node.js 18+ installed and configured                             │
│   ☐ Python 3.11+ with virtual environment                            │
│   ☐ All package dependencies installed                               │
│   ☐ Port availability verified (8000, 3002, 3003, 5177)             │
│                                                                        │
│ ☐ Security and Session Configuration                                  │
│   ☐ SESSION_SECRET configured (not default value)                    │
│   ☐ CORS origins configured for target environment                   │
│   ☐ SSL certificates available for production deployment             │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

### Post-Startup Validation
```
┌────────────────────────────────────────────────────────────────────────┐
│ POST-STARTUP FUNCTIONAL VALIDATION                                     │
├────────────────────────────────────────────────────────────────────────┤
│                                                                        │
│ ☐ Service Health and Connectivity                                     │
│   ☐ All services respond to health check endpoints                   │
│   ☐ Inter-service communication functioning correctly                │
│   ☐ Database queries execute without errors                          │
│   ☐ Session management working across requests                       │
│                                                                        │
│ ☐ Frontend Application Functionality                                  │
│   ☐ React application loads without console errors                   │
│   ☐ Graph visualization renders correctly                            │
│   ☐ Node expansion and interaction working                           │
│   ☐ Settings persistence functional                                  │
│                                                                        │
│ ☐ API Backend Functionality                                           │
│   ☐ Graph data endpoints return expected data                        │
│   ☐ GDS analysis endpoints functional                                │
│   ☐ Performance within acceptable thresholds                         │
│                                                                        │
│ ☐ AI Chat Functionality                                               │
│   ☐ Chat interface accepts and processes messages                    │
│   ☐ LLM provider responses received and formatted                    │
│   ☐ Context building from graph data working                         │
│   ☐ Conversation history persistence functional                      │
│                                                                        │
└────────────────────────────────────────────────────────────────────────┘
```

## 📚 Files and Scripts Reference

### Enhanced File Structure
```
KnowledgeGraphVisualizer/
├── start-services.sh                    # Enhanced startup script (shell)
├── test-startup-services.sh             # Comprehensive validation script
├── scripts/
│   ├── simple-dev.js                    # Node.js orchestration (simple)
│   ├── dev.js                           # Node.js orchestration (advanced)
│   └── validate-env.sh                  # Environment validation
├── docker-compose.yml                   # Production Docker orchestration
├── .github/workflows/
│   └── startup-validation.yml           # CI/CD pipeline integration
└── STARTUP_PROCESS_DOCUMENTATION.md     # This comprehensive guide
```

### Critical Script Usage
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Recommended Development Workflow                                │
# └─────────────────────────────────────────────────────────────────┘

# 1. Validate environment configuration
./scripts/validate-env.sh

# 2. Start all services with dependency management
npm run dev

# 3. Validate service health and functionality
./test-startup-services.sh

# 4. Monitor service logs during development
tail -f 360t-kg-api/logs/server.log &
tail -f proxy-server/logs/proxy.log &

# 5. Shutdown all services cleanly
# Ctrl+C in the npm run dev terminal
```

---

## 🎯 Next Steps and Recommendations

### Immediate Actions
1. **Use Enhanced Startup Process**: Always use `npm run dev` or the updated startup scripts
2. **Validate Service Health**: Run validation script after startup to confirm functionality
3. **Monitor Service Logs**: Keep logs open during development for real-time debugging
4. **Environment Configuration**: Ensure all required environment variables are properly configured

### Production Considerations
1. **Container Orchestration**: Implement Docker Compose for production deployment
2. **Service Monitoring**: Add comprehensive monitoring and alerting for all services
3. **Load Balancing**: Consider load balancers for high-availability deployments
4. **Security Hardening**: Implement proper SSL, authentication, and input validation

### Performance Optimization
1. **Caching Strategy**: Implement Redis for session storage and response caching
2. **Database Optimization**: Add proper indexes and query optimization for Neo4j
3. **Resource Monitoring**: Implement comprehensive resource monitoring and alerting
4. **Scaling Strategy**: Plan for horizontal scaling of compute-intensive services

**Last Updated**: January 2025 | **Version**: 2.0.0 | **Architecture**: 5-Layer Service Mesh