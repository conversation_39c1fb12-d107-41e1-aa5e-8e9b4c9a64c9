# Knowledge Graph Visualizer - Proxy Server

A high-performance Express.js proxy server that serves as the central routing hub for the Knowledge Graph Visualizer, managing session state, implementing rate limiting, and providing intelligent request routing between frontend clients and backend services.

## 🏗️ Proxy Architecture Overview

### Service Orchestration & Request Routing
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Knowledge Graph Proxy Server (Express.js - Port 3003)                      │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ Session Manager │ │ Request Router  │ │ Health Monitor  │               │
│ │ • Express Session│ │ • Intelligent   │ │ • Service Health│               │
│ │ • Memory Store   │ │   Route Logic   │ │ • Dependency    │               │
│ │ • Session Persist│ │ • Load Balancing│ │   Monitoring    │               │
│ │ • CSRF Protection│ │ • Failover      │ │ • Alert System  │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ CORS Handler    │ │ Rate Limiter    │ │ Request Logger  │               │
│ │ • Multi-Origin  │ │ • Per-Session   │ │ • Structured    │               │
│ │ • Dynamic Config│ │ • Sliding Window│ │   Logging       │               │
│ │ • Preflight     │ │ • Custom Rules  │ │ • Performance   │               │
│ │ • Credentials   │ │ • DDoS Protection│ │   Metrics       │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
└─────────────────────────────────────────────────────────────────────────────┘
                                  │
                    Intelligent Request Routing
                                  │
┌────────────────────────────────┬─┴──────────────────────────────────────────┐
│                                │                                           │
│    Backend API Requests        │           AI Chat Requests                │
│    (Graph, Analysis, Settings) │           (Chat, Conversations)           │
│                                │                                           │
│         ↓                      │                    ↓                      │
│ ┌─────────────────────────────┐│ ┌─────────────────────────────────────────┐│
│ │ API Backend (Port 3002)     ││ │ AI Layer FastAPI (Port 8000)            ││
│ │ • Neo4j Graph Queries       ││ │ • LLM Provider Management               ││
│ │ • GDS Analytics             ││ │ • Conversation History                  ││
│ │ • Settings Management       ││ │ • Context Building                      ││
│ │ • Performance Monitoring    ││ │ • Provider Failover                     ││
│ └─────────────────────────────┘│ └─────────────────────────────────────────┘│
└────────────────────────────────┴───────────────────────────────────────────┘
```

## 🔄 Request Flow Architecture

### Intelligent Routing Matrix
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Request Routing Decision Tree                                               │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ Frontend Request (Port 5177) → Proxy Server (Port 3003)                    │
│                                        │                                    │
│                              ┌─────────┴─────────┐                         │
│                              │ Route Analyzer    │                         │
│                              │ • Parse URL Path  │                         │
│                              │ • Check Headers   │                         │
│                              │ • Session Context │                         │
│                              └─────────┬─────────┘                         │
│                                        │                                    │
│                       ┌────────────────┼────────────────┐                  │
│                       │                │                │                  │
│                 /api/chat/*      /api/conversations/*  /api/**             │
│                 /api/stream/*                          (everything else)   │
│                       │                │                │                  │
│                       ▼                ▼                ▼                  │
│        ┌─────────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │
│        │ AI Layer Routing    │ │ AI Layer Route  │ │ Backend API Routing │ │
│        │ Target: Port 8000   │ │ Target: Port    │ │ Target: Port 3002   │ │
│        │ • Chat Processing   │ │ 8000            │ │ • Graph Queries     │ │
│        │ • LLM Providers     │ │ • Conversation  │ │ • Analytics         │ │
│        │ • Context Building  │ │   Management    │ │ • Settings          │ │
│        │ • Session Context   │ │ • History       │ │ • Health Checks     │ │
│        └─────────────────────┘ └─────────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Detailed Request Flow Pipeline
```
┌────────────────────────────────────────────────────────────────────────────┐
│ Complete Request Processing Pipeline                                       │
├────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ 1. Request Reception & Initial Processing                                  │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Receive HTTP request from frontend                             │   │
│    │ • Parse headers, body, and URL parameters                        │   │
│    │ • Extract session ID and correlation tokens                      │   │
│    │ • Log request details with timestamp                             │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 2. Security & Validation Layer                                             │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • CORS validation (origin, methods, headers)                     │   │
│    │ • Rate limiting check (per session/IP)                           │   │
│    │ • Session validation and renewal                                 │   │
│    │ • Input sanitization and basic validation                       │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 3. Route Decision & Service Selection                                      │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Analyze request path and determine target service              │   │
│    │ • Check target service health and availability                   │   │
│    │ • Apply service-specific routing rules                           │   │
│    │ • Select optimal backend instance (if load balancing)            │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 4. Request Transformation & Proxying                                       │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Transform request for target service compatibility             │   │
│    │ • Add proxy headers (X-Forwarded-For, X-Real-IP)                │   │
│    │ • Inject session context and correlation IDs                     │   │
│    │ • Forward request to selected backend service                    │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 5. Response Processing & Client Return                                     │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Receive response from backend service                          │   │
│    │ • Process response headers and status codes                      │   │
│    │ • Apply response transformations if needed                       │   │
│    │ • Update session state and performance metrics                   │   │
│    │ • Return response to frontend client                             │   │
│    └──────────────────────────────────────────────────────────────────┘   │
└────────────────────────────────────────────────────────────────────────────┘
```

## 🔧 Core Features & Capabilities

### Session Management Architecture
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Session Storage     │ Session Security    │ Session Persistence │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Express Session   │ • Secure Cookies    │ • Cross-browser     │
│ • Memory Store      │ • CSRF Tokens       │ • Tab Sync          │
│ • Redis Ready       │ • Session Rotation  │ • Auto-renewal      │
│ • Configurable TTL  │ • IP Validation     │ • Graceful Cleanup  │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Advanced CORS Management
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Origin Management   │ Method Control      │ Header Management   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Dynamic Origins   │ • RESTful Methods   │ • Custom Headers    │
│ • Wildcard Support  │ • Options Preflight │ • Authorization     │
│ • Environment Based │ • Method Validation │ • Content Types     │
│ • Security Policies │ • CORS Caching      │ • Request Context   │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Intelligent Rate Limiting
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Limiting Strategies │ Detection Methods   │ Protection Features │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Sliding Window    │ • IP-based Tracking │ • DDoS Mitigation   │
│ • Token Bucket      │ • Session-based     │ • Progressive Delays│
│ • Fixed Window      │ • User-based        │ • Temporary Bans    │
│ • Adaptive Limits   │ • Endpoint-specific │ • Alert Integration │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

## 🚀 Quick Start & Installation

### Prerequisites & Requirements
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Core Requirements   │ Version Details     │ Purpose             │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ Node.js             │ 18+ LTS             │ JavaScript runtime  │
│ npm                 │ 8+                  │ Package manager     │
│ Express.js          │ 4.18+               │ Web framework       │
│ Redis (Optional)    │ 6+                  │ Session store       │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Installation & Configuration
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development Environment Setup                                   │
# └─────────────────────────────────────────────────────────────────┘
# Navigate to proxy server directory
cd proxy-server

# Install dependencies
npm install

# Copy environment configuration template
cp .env.example .env

# Edit .env with your specific configuration
# See Environment Configuration section below

# Start development server with auto-reload
npm run dev                          # Development mode with nodemon
# OR
npm start                           # Production mode
```

### Environment Configuration Matrix
```env
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PROXY SERVER CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
PROXY_PORT=3003                         # Proxy server port
PROXY_HOST=0.0.0.0                      # Bind address (0.0.0.0 for all interfaces)
NODE_ENV=development                     # Environment (development/production)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# BACKEND SERVICE URLS
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
BACKEND_API_URL=http://localhost:3002   # API Backend service endpoint
FASTAPI_URL=http://localhost:8000       # AI Layer FastAPI service endpoint
FRONTEND_URL=http://localhost:5177      # Frontend application URL

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# SESSION MANAGEMENT
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
SESSION_SECRET=your-secret-key-change-in-production    # Session encryption key
SESSION_MAX_AGE=3600000                  # Session timeout (1 hour in milliseconds)
SESSION_SECURE=false                     # HTTPS-only cookies (true for production)
SESSION_SAME_SITE=lax                    # SameSite cookie policy (lax/strict/none)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# CORS CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
CORS_ALLOWED_ORIGINS=http://localhost:5177,http://localhost:5178,http://localhost:3000
CORS_ALLOW_CREDENTIALS=true             # Allow credentials in CORS requests
CORS_MAX_AGE=86400                       # Preflight cache duration (24 hours)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# RATE LIMITING CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
RATE_LIMIT_WINDOW_MS=900000              # Rate limit window (15 minutes)
RATE_LIMIT_MAX_REQUESTS=100              # Max requests per window
RATE_LIMIT_MESSAGE="Too many requests"   # Rate limit exceeded message
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false # Count only failed requests

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PERFORMANCE & MONITORING
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
ENABLE_REQUEST_LOGGING=true             # Enable detailed request logging
ENABLE_PERFORMANCE_METRICS=true         # Enable performance monitoring
LOG_LEVEL=info                          # Logging level (debug/info/warn/error)
HEALTH_CHECK_INTERVAL=30000             # Health check interval (30 seconds)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# REDIS SESSION STORE (Optional)
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
REDIS_URL=redis://localhost:6379        # Redis connection URL (optional)
REDIS_PREFIX=kg_session:                # Redis key prefix for sessions
REDIS_TTL=3600                          # Redis session TTL (1 hour)
```

## 🔌 Proxy Server API Reference

### Health & System Monitoring
```
┌────────────────────────────────────────────────────────────────────────┐
│ Health Check & Monitoring Endpoints                                   │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint              │ Description                            │
├────────┼───────────────────────┼────────────────────────────────────────┤
│ GET    │ /health               │ Comprehensive proxy health check      │
│        │                       │ • Service availability status         │
│        │                       │ • Backend connectivity checks         │
│        │                       │ • Session store health                │
│        │                       │ • Memory and performance metrics      │
├────────┼───────────────────────┼────────────────────────────────────────┤
│ GET    │ /health/detailed      │ Detailed health with dependencies     │
│        │                       │ • Individual service status           │
│        │                       │ • Response time measurements          │
│        │                       │ • Error rate statistics               │
│        │                       │ • Connection pool status              │
├────────┼───────────────────────┼────────────────────────────────────────┤
│ GET    │ /metrics              │ Prometheus metrics endpoint           │
│        │                       │ • Request/response time histograms    │
│        │                       │ • HTTP status code counters           │
│        │                       │ • Active session gauges               │
│        │                       │ • Backend service availability        │
├────────┼───────────────────────┼────────────────────────────────────────┤
│ GET    │ /status               │ Simple status check (uptime)          │
│        │                       │ • Basic availability confirmation     │
│        │                       │ • Uptime information                  │
│        │                       │ • Version information                 │
└────────┴───────────────────────┴────────────────────────────────────────┘
```

### Proxy Routing Endpoints
```
┌────────────────────────────────────────────────────────────────────────┐
│ Proxied Endpoints (Intelligent Routing)                               │
├────────────────────────────────────────────────────────────────────────┤
│ Route Pattern         │ Target Service   │ Description                  │
├───────────────────────┼──────────────────┼──────────────────────────────┤
│ /api/chat             │ AI Layer (8000)  │ Chat message processing      │
│ /api/chat/stream      │ AI Layer (8000)  │ Real-time streaming chat     │
│ /api/conversations    │ AI Layer (8000)  │ Conversation management      │
│ /api/conversations/*  │ AI Layer (8000)  │ Conversation operations      │
├───────────────────────┼──────────────────┼──────────────────────────────┤
│ /api/graph/*          │ Backend (3002)   │ Graph data operations        │
│ /api/analysis/*       │ Backend (3002)   │ GDS analytics                │
│ /api/settings/*       │ Backend (3002)   │ Settings management          │
│ /api/ollama/*         │ Backend (3002)   │ Local LLM integration        │
│ /api/health           │ Backend (3002)   │ Backend health checks        │
├───────────────────────┼──────────────────┼──────────────────────────────┤
│ /api/**               │ Backend (3002)   │ Default backend routing      │
│ (fallback)            │                  │ All unmatched API routes     │
└───────────────────────┴──────────────────┴──────────────────────────────┘
```

## 🧠 Advanced Routing Logic

### Smart Routing Decision Tree
```javascript
// Proxy routing intelligence
const routingDecisionTree = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ AI-Related Requests (High Priority)                            │
  // └─────────────────────────────────────────────────────────────────┘
  aiRoutes: {
    patterns: ['/api/chat', '/api/conversations', '/api/stream'],
    target: 'http://localhost:8000',
    characteristics: {
      sessionRequired: true,
      rateLimiting: 'relaxed',
      timeout: 60000,  // 60 seconds for LLM processing
      retries: 1,
      streamingCapable: true
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Graph & Analytics Requests (Medium Priority)                   │
  // └─────────────────────────────────────────────────────────────────┘
  graphRoutes: {
    patterns: ['/api/graph', '/api/analysis', '/api/settings', '/api/ollama'],
    target: 'http://localhost:3002',
    characteristics: {
      sessionOptional: true,
      rateLimiting: 'standard',
      timeout: 30000,  // 30 seconds for complex queries
      retries: 2,
      cacheable: true
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ System & Health Requests (Low Priority)                        │
  // └─────────────────────────────────────────────────────────────────┘
  systemRoutes: {
    patterns: ['/api/health', '/api/metrics', '/api/system'],
    target: 'http://localhost:3002',
    characteristics: {
      sessionNotRequired: true,
      rateLimiting: 'strict',
      timeout: 5000,   // 5 seconds for health checks
      retries: 0,
      cacheable: false
    }
  }
};
```

### Load Balancing & Failover Strategy
```javascript
// Advanced routing with failover
const loadBalancingConfig = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Backend Service Pool Management                                 │
  // └─────────────────────────────────────────────────────────────────┘
  backendPool: {
    primary: 'http://localhost:3002',
    fallback: ['http://localhost:3004', 'http://localhost:3005'],
    healthCheck: {
      endpoint: '/health',
      interval: 30000,
      timeout: 5000,
      retries: 3
    },
    loadBalancing: {
      strategy: 'round-robin', // round-robin, least-connections, weighted
      weights: { primary: 70, fallback: 30 },
      stickySessions: true
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ AI Service Pool Management                                      │
  // └─────────────────────────────────────────────────────────────────┘
  aiPool: {
    primary: 'http://localhost:8000',
    fallback: ['http://localhost:8001'],
    healthCheck: {
      endpoint: '/health',
      interval: 60000,
      timeout: 10000,
      retries: 2
    },
    circuitBreaker: {
      failureThreshold: 5,
      timeout: 60000,
      resetTimeout: 300000
    }
  }
};
```

## 🛡️ Security & Performance Features

### Advanced Security Implementation
```javascript
// Comprehensive security middleware stack
const securityFeatures = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ CORS Security Configuration                                     │
  // └─────────────────────────────────────────────────────────────────┘
  corsConfig: {
    origin: function (origin, callback) {
      const allowedOrigins = process.env.CORS_ALLOWED_ORIGINS?.split(',') || [
        'http://localhost:5177',    // Primary frontend
        'http://localhost:5178',    // Vite fallback
        'http://localhost:3000',    // Development port
        'http://localhost:4173'     // Preview port
      ];
      
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('CORS: Origin not allowed'), false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization', 
      'X-Requested-With',
      'X-Session-ID',
      'X-Request-ID'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Response-Time',
      'X-Request-ID'
    ],
    maxAge: 86400 // 24 hours
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Rate Limiting Strategy                                          │
  // └─────────────────────────────────────────────────────────────────┘
  rateLimiting: {
    // General API rate limiting
    general: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
      message: 'Too many requests from this IP',
      standardHeaders: true,
      legacyHeaders: false
    },
    
    // Chat-specific rate limiting (more lenient)
    chat: {
      windowMs: 1 * 60 * 1000,   // 1 minute
      maxRequests: 20,
      message: 'Too many chat requests, please slow down'
    },
    
    // Strict rate limiting for expensive operations
    analytics: {
      windowMs: 5 * 60 * 1000,   // 5 minutes
      maxRequests: 10,
      message: 'Analytics operations are rate limited'
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Session Security Configuration                                  │
  // └─────────────────────────────────────────────────────────────────┘
  sessionSecurity: {
    secret: process.env.SESSION_SECRET || 'fallback-secret-change-in-production',
    resave: false,
    saveUninitialized: false,
    rolling: true, // Extend session on activity
    cookie: {
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      httpOnly: true, // Prevent XSS
      maxAge: parseInt(process.env.SESSION_MAX_AGE) || 3600000, // 1 hour
      sameSite: process.env.SESSION_SAME_SITE || 'lax'
    },
    name: 'kg.sid', // Custom session name
    genid: () => require('crypto').randomBytes(16).toString('hex')
  }
};
```

### Performance Optimization Features
```javascript
// Performance enhancement configuration
const performanceOptimizations = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Request/Response Compression                                    │
  // └─────────────────────────────────────────────────────────────────┘
  compression: {
    level: 6,           // Compression level (1-9)
    threshold: 1024,    // Only compress responses > 1KB
    filter: (req, res) => {
      // Don't compress streaming responses
      if (req.headers['accept'] === 'text/event-stream') return false;
      return require('compression').filter(req, res);
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Request Timeout Management                                      │
  // └─────────────────────────────────────────────────────────────────┘
  timeouts: {
    server: 120000,     // Server timeout (2 minutes)
    proxy: {
      '/api/chat': 60000,        // Chat requests: 60 seconds
      '/api/analysis': 45000,    // Analytics: 45 seconds  
      '/api/graph': 30000,       // Graph queries: 30 seconds
      'default': 15000           // Default: 15 seconds
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Caching Strategy                                                │
  // └─────────────────────────────────────────────────────────────────┘
  caching: {
    static: {
      maxAge: 3600000,  // 1 hour for static resources
      etag: true,
      lastModified: true
    },
    api: {
      '/api/health': 30000,      // Cache health checks for 30 seconds
      '/api/ollama/models': 300000, // Cache model list for 5 minutes
      '/api/settings': 60000     // Cache settings for 1 minute
    }
  }
};
```

## 📊 Monitoring & Observability

### Comprehensive Metrics Collection
```javascript
// Performance metrics and monitoring
const monitoringConfig = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Request Performance Metrics                                     │
  // └─────────────────────────────────────────────────────────────────┘
  requestMetrics: {
    responseTime: 'histogram',     // Response time distribution
    requestCount: 'counter',       // Total requests counter
    errorRate: 'gauge',           // Error rate percentage
    activeConnections: 'gauge',    // Current active connections
    throughput: 'gauge'           // Requests per second
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ System Resource Metrics                                         │
  // └─────────────────────────────────────────────────────────────────┘
  systemMetrics: {
    memoryUsage: 'gauge',         // Memory utilization
    cpuUsage: 'gauge',            // CPU utilization
    eventLoopLag: 'histogram',    // Node.js event loop lag
    gcDuration: 'histogram'       // Garbage collection duration
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Backend Service Health Metrics                                  │
  // └─────────────────────────────────────────────────────────────────┘
  serviceMetrics: {
    backendApiHealth: 'gauge',    // Backend API availability (0/1)
    aiLayerHealth: 'gauge',       // AI Layer availability (0/1)  
    backendResponseTime: 'histogram', // Backend response times
    aiLayerResponseTime: 'histogram'  // AI Layer response times
  }
};
```

### Health Check Implementation
```javascript
// Comprehensive health checking system
const healthChecks = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Service Availability Checks                                     │
  // └─────────────────────────────────────────────────────────────────┘
  async checkBackendAPI() {
    try {
      const response = await axios.get(`${process.env.BACKEND_API_URL}/health`, {
        timeout: 5000
      });
      return {
        service: 'backend-api',
        status: response.status === 200 ? 'healthy' : 'unhealthy',
        responseTime: response.headers['x-response-time'] || 'unknown',
        details: response.data
      };
    } catch (error) {
      return {
        service: 'backend-api',
        status: 'unhealthy',
        error: error.message,
        lastCheck: new Date().toISOString()
      };
    }
  },

  async checkAILayer() {
    try {
      const response = await axios.get(`${process.env.FASTAPI_URL}/health`, {
        timeout: 10000
      });
      return {
        service: 'ai-layer',
        status: response.status === 200 ? 'healthy' : 'unhealthy',
        responseTime: response.headers['x-response-time'] || 'unknown',
        providers: response.data.available_providers || []
      };
    } catch (error) {
      return {
        service: 'ai-layer', 
        status: 'unhealthy',
        error: error.message,
        lastCheck: new Date().toISOString()
      };
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Comprehensive Health Status                                     │
  // └─────────────────────────────────────────────────────────────────┘
  async getOverallHealth() {
    const [backendHealth, aiHealth] = await Promise.all([
      this.checkBackendAPI(),
      this.checkAILayer()
    ]);

    const allHealthy = backendHealth.status === 'healthy' && 
                      aiHealth.status === 'healthy';

    return {
      status: allHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      services: {
        proxy: { status: 'healthy', port: process.env.PROXY_PORT || 3003 },
        backend: backendHealth,
        ai: aiHealth
      },
      system: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        version: process.version
      }
    };
  }
};
```

## 🧪 Testing Strategy

### Test Categories & Coverage
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Unit Tests          │ Integration Tests   │ E2E Tests           │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Middleware Tests  │ • Proxy Routing     │ • Full Workflow     │  
│ • Routing Logic     │ • Session Management│ • Error Scenarios   │
│ • Security Features │ • CORS Handling     │ • Performance Tests │
│ • Health Checks     │ • Rate Limiting     │ • Load Testing      │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Test Execution Commands
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Test Suite Execution                                            │
# └─────────────────────────────────────────────────────────────────┘
npm test                             # Full test suite (Mocha)
npm run test:unit                    # Unit tests only
npm run test:integration             # Integration tests only  
npm run test:watch                   # Watch mode for development
npm run test:coverage                # Coverage reports

# ┌─────────────────────────────────────────────────────────────────┐
# │ Performance & Load Testing                                      │
# └─────────────────────────────────────────────────────────────────┘
npm run test:performance             # Performance benchmarks
npm run test:load                    # Load testing with artillery
npm run test:security                # Security vulnerability testing
```

## 🐛 Troubleshooting Guide

### Common Configuration Issues
```
┌─────────────────────────────────────────────────────────────────────┐
│ Error: "CORS: Origin not allowed"                                  │
├─────────────────────────────────────────────────────────────────────┤
│ Cause: Frontend origin not in CORS_ALLOWED_ORIGINS                 │
│ Solutions:                                                          │
│ • Add frontend URL to CORS_ALLOWED_ORIGINS environment variable    │
│ • Check for trailing slashes in URLs                               │
│ • Verify protocol (http/https) matches                             │
│ • Test with curl: curl -H "Origin: http://localhost:5177" ...      │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "Backend service unavailable"                               │
├─────────────────────────────────────────────────────────────────────┤
│ Cause: Target backend services not running or misconfigured        │
│ Solutions:                                                          │
│ • Verify backend API is running: curl http://localhost:3002/health │
│ • Verify AI layer is running: curl http://localhost:8000/health    │
│ • Check BACKEND_API_URL and FASTAPI_URL environment variables      │
│ • Review proxy server logs for connection errors                   │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "Session store connection failed"                           │
├─────────────────────────────────────────────────────────────────────┤
│ Cause: Redis connection issues (if using Redis for sessions)       │
│ Solutions:                                                          │
│ • Check Redis server status: redis-cli ping                        │
│ • Verify REDIS_URL environment variable                            │
│ • Fallback to memory store by commenting out Redis configuration   │
│ • Check Redis server logs for connection errors                    │
└─────────────────────────────────────────────────────────────────────┘
```

### Performance Troubleshooting
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Performance Diagnostics                                         │
# └─────────────────────────────────────────────────────────────────┘
# Check proxy server performance metrics
curl http://localhost:3003/metrics

# Monitor active connections and memory usage
curl http://localhost:3003/health/detailed

# Test response times to backend services
time curl http://localhost:3003/api/health
time curl http://localhost:3003/api/chat -X POST -d '{"message":"test"}'

# Check for memory leaks
NODE_ENV=development npm start    # Enables memory monitoring
```

## 📁 Project Structure

### Detailed Directory Layout
```
proxy-server/
├── 📄 server.js                     # Main proxy server entry point
├── 📁 routes/                       # Route handlers
│   ├── 📄 chat.js                   # Chat routing with session management
│   ├── 📄 health.js                 # Health check routes
│   └── 📄 proxy.js                  # Main proxy routing logic
├── 📁 middleware/                   # Express middleware
│   ├── 📄 cors.js                   # CORS configuration
│   ├── 📄 session.js                # Session management setup
│   ├── 📄 rateLimiting.js           # Rate limiting middleware
│   ├── 📄 logging.js                # Request/response logging
│   ├── 📄 security.js               # Security headers & validation
│   └── 📄 performance.js            # Performance monitoring
├── 📁 config/                       # Configuration files
│   ├── 📄 environment.js            # Environment variable management
│   ├── 📄 routes.js                 # Routing configuration
│   └── 📄 security.js               # Security policies
├── 📁 utils/                        # Utility functions
│   ├── 📄 healthCheck.js            # Health checking utilities
│   ├── 📄 metrics.js                # Performance metrics collection
│   └── 📄 logger.js                 # Logging configuration
├── 📁 tests/                        # Test suite
│   ├── 📁 unit/                     # Unit tests
│   ├── 📁 integration/              # Integration tests
│   └── 📁 fixtures/                 # Test data
├── 📄 package.json                  # Dependencies & scripts
├── 📄 .env.example                  # Environment template
└── 📄 README.md                     # This documentation
```

## 🚀 Production Deployment

### Docker Configuration
```dockerfile
# Multi-stage Docker build for proxy server
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

# Security: Run as non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S proxy -u 1001
USER proxy

EXPOSE 3003
CMD ["npm", "start"]
```

### Production Environment Setup
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Production Deployment Checklist                                │
# └─────────────────────────────────────────────────────────────────┘
# Environment validation
npm run validate-env
npm run test:security

# SSL/TLS setup (if terminating SSL at proxy level)
export HTTPS_ENABLED=true
export SSL_CERT_PATH=/etc/ssl/certs/proxy.crt
export SSL_KEY_PATH=/etc/ssl/private/proxy.key

# Process management with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup

# Health monitoring setup
curl http://localhost:3003/health/detailed
curl http://localhost:3003/metrics
```

## 🤝 Contributing Guidelines

### Development Standards
- **Code Quality**: ESLint + Prettier enforced
- **Security**: OWASP security guidelines followed
- **Testing**: 80%+ test coverage required
- **Documentation**: Comprehensive inline documentation

### Pull Request Process
1. Fork repository and create feature branch
2. Implement changes with tests
3. Verify security and performance impact
4. Update documentation
5. Submit PR with detailed description

---

**Last Updated**: January 2025 | **Version**: 2.0.0 | **Architecture**: Express.js Proxy + Session Management + Intelligent Routing