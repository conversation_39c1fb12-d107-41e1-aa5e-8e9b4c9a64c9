import React, { useState } from 'react';
import { formatTextWithNodeLinks } from '../utils/textFormatting';
/* Removed legacy component-level NodeDetailsModern.css to avoid style collisions */
/* import './NodeDetailsModern.css'; */

/**
 * Demo component to showcase the enhanced text formatting functionality
 * with all the new improvements
 */
const EnhancedTextFormattingDemo = () => {
  const [selectedNode, setSelectedNode] = useState(null);

  // Mock relationships data for comprehensive demonstration
  const mockRelationships = [
    {
      type: 'USES',
      direction: 'outgoing',
      node: {
        id: 'margin-1',
        properties: {
          name: 'Margin',
          category: 'Financial'
        }
      }
    },
    {
      type: 'CONTAINS',
      direction: 'outgoing',
      node: {
        id: 'fixing-ref-groups-1',
        properties: {
          name: 'Fixing Reference Groups',
          category: 'Configuration'
        }
      }
    },
    {
      type: 'PROCESSES',
      direction: 'outgoing',
      node: {
        id: 'trading-system-1',
        properties: {
          name: 'Trading System',
          category: 'Core Module'
        }
      }
    },
    {
      type: 'VALIDATES',
      direction: 'incoming',
      node: {
        id: 'party-1',
        properties: {
          name: 'Party',
          category: 'Entity'
        }
      }
    },
    {
      type: 'CONFIGURES',
      direction: 'outgoing',
      node: {
        id: 'ems-1',
        properties: {
          name: 'EMS',
          category: 'Trading System'
        }
      }
    }
  ];

  // Sample texts demonstrating all the improvements
  const demonstrationTexts = [
    {
      title: "1. Multi-word Node Name Detection",
      description: "Tests detection of multi-word node names like 'Fixing Reference Groups'",
      text: "The Fixing Reference Groups are configuration groups within the ADS system used for classifying fixing references in trading workflows."
    },
    {
      title: "2. Case-Insensitive Matching",
      description: "Tests that MARGIN, Margin, and margin are all detected as the same entity",
      text: "The MARGIN system processes margin calculations. Each Margin is validated and all margins are stored securely."
    },
    {
      title: "3. Plural/Singular Detection",
      description: "Tests intelligent matching of plural and singular forms",
      text: "The system processes margins and each margin is validated. The parties involved and each party must agree on the terms."
    },
    {
      title: "4. Complex Multi-word with Plurals",
      description: "Tests multi-word names with plural/singular variations",
      text: "The Fixing Reference Groups and each fixing reference group work together to ensure proper classification."
    },
    {
      title: "5. Mixed Case and Punctuation",
      description: "Tests various capitalizations and punctuation handling",
      text: "Components like EMS, ems, and Ems work with the Trading System. The margins, Margins, and MARGINS are all processed."
    },
    {
      title: "6. Complex Sentence Structure",
      description: "Tests detection in complex sentences with multiple node references",
      text: "When the Trading System receives a request, it validates through the EMS, processes margins via the Fixing Reference Groups, and ensures each party and all parties are properly authenticated."
    }
  ];

  const handleNodeClick = (node) => {
    setSelectedNode(node);
    console.log('Node clicked:', node.properties.name);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '900px', margin: '0 auto' }}>
      <h1 style={{ color: '#00973A', marginBottom: '20px' }}>
        Enhanced Node Name Highlighting Demo
      </h1>
      
      <div style={{ marginBottom: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3 style={{ color: '#00973A', marginTop: '0' }}>✅ Improvements Implemented:</h3>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginTop: '15px' }}>
          <div>
            <h4 style={{ color: '#333', margin: '0 0 8px 0' }}>🔍 Detection Improvements:</h4>
            <ul style={{ lineHeight: '1.6', margin: '0', paddingLeft: '20px' }}>
              <li>Multi-word node names (e.g., "Fixing Reference Groups")</li>
              <li>Case-insensitive matching (MARGIN = margin = Margin)</li>
              <li>Plural/singular detection (margin ↔ margins)</li>
              <li>Complex pluralization (party ↔ parties)</li>
            </ul>
          </div>
          <div>
            <h4 style={{ color: '#333', margin: '0 0 8px 0' }}>🎨 Visual Improvements:</h4>
            <ul style={{ lineHeight: '1.6', margin: '0', paddingLeft: '20px' }}>
              <li>Removed light green background</li>
              <li>Clean bold text with 360T green color</li>
              <li>Subtle hover effects</li>
              <li>Professional appearance</li>
            </ul>
          </div>
        </div>
      </div>

      {demonstrationTexts.map((demo, index) => (
        <div key={index} style={{ 
          marginBottom: '25px', 
          padding: '20px', 
          border: '1px solid #e2e8f0', 
          borderRadius: '8px',
          backgroundColor: 'white'
        }}>
          <h3 style={{ color: '#333', marginTop: '0', marginBottom: '8px' }}>
            {demo.title}
          </h3>
          <p style={{ color: '#666', fontSize: '14px', marginBottom: '15px', fontStyle: 'italic' }}>
            {demo.description}
          </p>
          <div style={{ 
            fontSize: '16px', 
            lineHeight: '1.7',
            padding: '15px',
            backgroundColor: '#fafafa',
            borderRadius: '6px',
            border: '1px solid #eee'
          }}>
            {formatTextWithNodeLinks(demo.text, mockRelationships, handleNodeClick)}
          </div>
        </div>
      ))}

      {selectedNode && (
        <div style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: 'white',
          padding: '25px',
          border: '2px solid #00973A',
          borderRadius: '8px',
          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          minWidth: '350px'
        }}>
          <h3 style={{ color: '#00973A', marginTop: '0', marginBottom: '15px' }}>
            🎯 Node Selected
          </h3>
          <div style={{ marginBottom: '15px' }}>
            <p style={{ margin: '5px 0' }}><strong>Name:</strong> {selectedNode.properties.name}</p>
            <p style={{ margin: '5px 0' }}><strong>Category:</strong> {selectedNode.properties.category}</p>
            <p style={{ margin: '5px 0' }}><strong>ID:</strong> {selectedNode.id}</p>
          </div>
          <button 
            onClick={() => setSelectedNode(null)}
            style={{
              backgroundColor: '#00973A',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '600'
            }}
          >
            Close
          </button>
        </div>
      )}

      <div style={{ 
        marginTop: '30px', 
        padding: '20px', 
        backgroundColor: '#e8f5e8', 
        borderRadius: '8px',
        border: '1px solid #00973A'
      }}>
        <h3 style={{ color: '#00973A', marginTop: '0' }}>🧪 How to Test:</h3>
        <ol style={{ lineHeight: '1.6' }}>
          <li>Click on any <span className="relationship-term node-link" style={{ 
            color: '#00973A', 
            fontWeight: '700',
            cursor: 'pointer'
          }}>highlighted node name</span> above</li>
          <li>Notice how multi-word names like "Fixing Reference Groups" are properly detected</li>
          <li>Observe case-insensitive matching (MARGIN, Margin, margin all work)</li>
          <li>See plural/singular detection (margin ↔ margins, party ↔ parties)</li>
          <li>Check the clean styling with bold green text and no background</li>
        </ol>
      </div>
    </div>
  );
};

export default EnhancedTextFormattingDemo;
