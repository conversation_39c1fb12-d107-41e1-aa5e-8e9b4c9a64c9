import React, { useState } from 'react';
import { formatTextWithNodeLinks } from '../utils/textFormatting';
/* Removed legacy component-level NodeDetailsModern.css to avoid style collisions */
/* import './NodeDetailsModern.css'; */

/**
 * Demo component to showcase the enhanced text formatting functionality
 */
const TextFormattingDemo = () => {
  const [selectedNode, setSelectedNode] = useState(null);

  // Mock relationships data for demonstration
  const mockRelationships = [
    {
      type: 'USES',
      direction: 'outgoing',
      node: {
        id: 'ems-1',
        properties: {
          name: 'EMS',
          category: 'Trading System'
        }
      }
    },
    {
      type: 'CONNECTS_TO',
      direction: 'outgoing',
      node: {
        id: 'trading-system-1',
        properties: {
          name: 'Trading System',
          category: 'Core Module'
        }
      }
    },
    {
      type: 'FEEDS_FROM',
      direction: 'incoming',
      node: {
        id: 'market-data-1',
        properties: {
          name: 'Market Data Feed',
          category: 'Data Source'
        }
      }
    },
    {
      type: 'VALIDATES',
      direction: 'outgoing',
      node: {
        id: 'risk-engine-1',
        properties: {
          name: 'Risk Engine',
          category: 'Validation'
        }
      }
    },
    {
      type: 'PROCESSES',
      direction: 'outgoing',
      node: {
        id: 'order-management-1',
        properties: {
          name: 'Order Management System',
          category: 'Processing'
        }
      }
    }
  ];

  // Sample text with various node name patterns
  const sampleTexts = [
    {
      title: "Single-word Node Names",
      text: "The EMS system processes orders and connects to various components for trading operations."
    },
    {
      title: "Multi-word Node Names",
      text: "The Trading System integrates with Market Data Feed to provide real-time information for the Order Management System."
    },
    {
      title: "Mixed Case and Punctuation",
      text: "Components like EMS, Trading System, and Market Data Feed work together. The Risk Engine validates all transactions."
    },
    {
      title: "Complex Sentence Structure",
      text: "When the Order Management System receives a request, it validates through the Risk Engine, processes via the Trading System, and updates the EMS with real-time data from the Market Data Feed."
    }
  ];

  const handleNodeClick = (node) => {
    setSelectedNode(node);
    console.log('Node clicked:', node.properties.name);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ color: '#00973A', marginBottom: '20px' }}>
        Enhanced Text Formatting Demo
      </h1>
      
      <div style={{ marginBottom: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3 style={{ color: '#00973A', marginTop: '0' }}>Improvements Made:</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>Multi-word node matching:</strong> Now correctly highlights "Trading System" and "Market Data Feed"</li>
          <li><strong>Light green color scheme:</strong> Changed from purple to 360T green (#00973A)</li>
          <li><strong>Removed domain term highlighting:</strong> Only actual node names are highlighted</li>
          <li><strong>Better coverage:</strong> All related nodes mentioned in text are now highlighted</li>
          <li><strong>Case-insensitive matching:</strong> Works with different capitalizations</li>
        </ul>
      </div>

      {sampleTexts.map((sample, index) => (
        <div key={index} style={{ 
          marginBottom: '25px', 
          padding: '15px', 
          border: '1px solid #e2e8f0', 
          borderRadius: '8px',
          backgroundColor: 'white'
        }}>
          <h3 style={{ color: '#333', marginTop: '0', marginBottom: '10px' }}>
            {sample.title}
          </h3>
          <div style={{ 
            fontSize: '16px', 
            lineHeight: '1.6',
            padding: '10px',
            backgroundColor: '#fafafa',
            borderRadius: '4px'
          }}>
            {formatTextWithNodeLinks(sample.text, mockRelationships, handleNodeClick)}
          </div>
        </div>
      ))}

      {selectedNode && (
        <div style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: 'white',
          padding: '20px',
          border: '2px solid #00973A',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          minWidth: '300px'
        }}>
          <h3 style={{ color: '#00973A', marginTop: '0' }}>Node Selected</h3>
          <p><strong>Name:</strong> {selectedNode.properties.name}</p>
          <p><strong>Category:</strong> {selectedNode.properties.category}</p>
          <p><strong>ID:</strong> {selectedNode.id}</p>
          <button 
            onClick={() => setSelectedNode(null)}
            style={{
              backgroundColor: '#00973A',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Close
          </button>
        </div>
      )}

      <div style={{ 
        marginTop: '30px', 
        padding: '15px', 
        backgroundColor: '#e8f5e8', 
        borderRadius: '8px',
        border: '1px solid #00973A'
      }}>
        <h3 style={{ color: '#00973A', marginTop: '0' }}>How to Test:</h3>
        <ol style={{ lineHeight: '1.6' }}>
          <li>Click on any <span className="relationship-term node-link" style={{ 
            color: '#00973A', 
            background: 'rgba(0, 151, 58, 0.1)', 
            padding: '1px 2px', 
            borderRadius: '2px',
            cursor: 'pointer'
          }}>highlighted node name</span> above</li>
          <li>A modal will appear showing the node details</li>
          <li>Notice the light green color scheme matching the 360T brand</li>
          <li>Observe that all related node names are properly highlighted</li>
        </ol>
      </div>
    </div>
  );
};

export default TextFormattingDemo;
