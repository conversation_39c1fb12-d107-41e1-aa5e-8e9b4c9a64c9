/* Modern Header Component Styles */
/* Building upon the 360T theme foundation */

.modern-header {
  position: sticky;
  top: 0;
  z-index: 1000;  /* Highest priority - prevents overlapping with other components */
  background-color: var(--360t-white);
  border-bottom: 1px solid var(--360t-border);
  box-shadow: var(--360t-shadow-lg);
  font-family: var(--360t-font-family-primary);
  height: var(--header-height);  /* Use consistent header height */

  /* NEW: grid layout to prevent overlap between brand | main | actions */
  display: grid;
  grid-template-columns: auto minmax(0, 1fr) auto;
  align-items: center;
  column-gap: var(--360t-space-5);
}

/* Main Header Bar */
.header-main {
  /* Occupies the middle grid column; no absolute/fixed positioning */
  grid-column: 2;
  display: flex;
  align-items: center;
  justify-content: center; /* center the nav group in the middle column */
  width: 100%;
  padding: var(--360t-space-2) var(--360t-space-5);
  height: 100%;
  gap: var(--360t-space-5);

  /* Create a safe background layer that never covers content */
  position: relative;
}
.header-main::before {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--360t-white);         /* same as header bg */
  z-index: 0;                             /* behind the content */
  pointer-events: none;                   /* never block interactions */
}

/* Brand Section */
.header-brand {
  grid-column: 1;
  flex-shrink: 0;
  position: relative;
  z-index: 1; /* above header-main background */
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--360t-space-3);
}

.brand-logo {
  /* Slightly smaller logo to match slimmer header */
  height: 36px;
  width: auto;
  display: block;
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-1);
}

.brand-title {
  font-size: var(--360t-text-xl);
  font-weight: var(--360t-font-bold);
  line-height: var(--360t-leading-tight);
  color: var(--360t-text);
  margin: 0;
}

.brand-subtitle {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  color: var(--360t-dark-gray);
  line-height: var(--360t-leading-tight);
}

/* Primary Navigation */
.header-nav {
  flex: 1 1 auto;
  display: flex;
  justify-content: center;          /* center the whole group */
  align-items: center;
  min-width: 0;                     /* allow children to shrink properly */
  /* Container-aware sizing to resist zoom overflow */
  max-width: none;                 /* allow full middle column width */
  margin-inline: auto;
  width: 100%;
  /* Keep nav horizontal and allow graceful horizontal scroll as last resort */
  overflow-x: auto;
  overflow-y: hidden;
  gap: var(--360t-space-3);
  scrollbar-width: none;            /* Firefox hide scrollbars */

  /* Ensure nav content is above the decorative background */
  position: relative;
  z-index: 1;
}
.header-nav::-webkit-scrollbar {     /* WebKit hide scrollbars */
  display: none;
}

.nav-list {
  display: inline-flex;              /* shrink-to-fit the items */
  flex-wrap:nowrap;                 /* Keep segmented control style - no wrapping */
  list-style:none ;
  margin: 1px;
  padding: 4px 8px;                  /* Restore original compact spacing for segmented rail */
  gap: 1px;
  background-color: var(--360t-light-gray);
  border-radius: var(--360t-radius-xl);
  border: 1px;
  border-color: var(--360t-light-gray);
  white-space: nowrap;
  width:fit-content;                /* Size based on content while maintaining segmented control appearance */
  max-width:fit-content;                   /* allow rail to grow if items are wider */
  overflow:hidden;                  /* clip active chip borders so they don't bleed out */
  flex-direction: row;
}

.nav-item {
  margin: none;
  flex: 0 0 auto;
  display:flex;
  flex-direction: row;
  align-items: center;
  min-width: 0;          /* allow text truncation on zoom */
  max-width:fit-content;      /* cap each chip */

}

.nav-button {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  /* segmented-control chip sizing */
  padding: 4px 8px;
  border-radius: calc(var(--360t-radius-xl) - 4px); /* slightly tighter than rail to avoid touching */
  border: 2px solid transparent;   /* reserve space so active border doesn't grow outward */
  background: transparent;
  color: var(--360t-dark-gray);
  font-size: 0.9rem; /* slightly smaller than text-sm */
  font-weight: var(--360t-font-medium);
  font-family: inherit;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  white-space: nowrap;
  min-height: 36px;              /* slightly smaller tap target while staying accessible */
  flex: 0 0 auto;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-button:hover {
  color: var(--360t-text);
  background-color: var(--360t-white);
  box-shadow: var(--360t-shadow-sm);
  transform: translateY(-1px);
}
/* Remove the vertical divider/line that appears next to items on hover
   Some browsers render a focus/hover outline/after element; force none. */
.nav-item::before,
.nav-item::after,
.nav-button::before,
.nav-button::after {
  content: none !important;
  border: 0 !important;
  box-shadow: none !important;
}

.nav-button.active {
  color: var(--360t-white);
  background-color: var(--360t-primary);
  border-color: var(--360t-primary);     /* visible in the reserved 2px border box */
  box-shadow: var(--360t-shadow-md);
  font-weight: var(--360t-font-semibold);
}

/* Keep active state without extra underline slug to avoid visual line artifacts */
.nav-button.active::after {
  content: none;
}

.nav-icon {
  color: currentColor;
  flex-shrink: 0;
}

.nav-text {
  font-weight: inherit;
  max-width: 110px;           /* limit text width inside the button */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Header Actions */
.header-actions {
  grid-column: 3;
  display: flex;
  align-items: center;
  gap: var(--360t-space-3);
  flex-shrink: 0;
  position: relative;
  z-index: 1; /* above header-main background, below focused overlays */
}

/* Unified action button style (icon or text) */
.header-actions .action-button,
.header-actions .settings-button,
.header-actions .help-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--360t-space-2);
  height: 36px;
  padding: 0 var(--360t-space-3);
  border-radius: var(--360t-radius-lg);
  border: 1px solid var(--360t-mid-gray);
  background: var(--360t-white);
  color: var(--360t-dark-gray);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-actions .action-button:hover,
.header-actions .settings-button:hover,
.header-actions .help-button:hover {
  background: var(--360t-light-gray);
  border-color: var(--360t-primary);
  color: var(--360t-primary);
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
}

/* Icon-only compact variant */
.header-actions .icon-only {
  width: 36px;
  padding: 0;
}

/* Graphiti settings should look like a gear icon button */
.header-actions .settings-button svg {
  width: 18px;
  height: 18px;
  color: currentColor;
}

/* Help button as question mark icon/text */
.header-actions .help-button svg {
  width: 18px;
  height: 18px;
  color: currentColor;
}

/* Make icons inside secondary action buttons white on green background */
.secondary-actions .action-button svg {
  color: var(--360t-white);
  stroke: var(--360t-white);
  fill: none;
}

/* Enhanced 2D/3D Toggle */
.view-mode-toggle {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);                 /* tighter gap to match action area */
  padding: var(--360t-space-2);             /* smaller container padding */
  background-color: var(--360t-white);
  border-radius: var(--360t-radius-lg);     /* smaller radius to align with buttons */
  border: 1px solid var(--360t-mid-gray);   /* thinner border */
  box-shadow: var(--360t-shadow-sm);
}

.toggle-label {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  color: var(--360t-dark-gray);
  white-space: nowrap;
  margin: 0;
}

.toggle-group {
  display: flex;
  background-color: var(--360t-white);
  border-radius: var(--360t-radius-md);
  overflow: hidden;
  box-shadow: var(--360t-shadow-sm);
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  padding: 0 var(--360t-space-3);           /* horizontal padding only */
  height: 36px;                             /* match .action-button/.settings-button height */
  border: 1px solid transparent;
  background: transparent;
  color: var(--360t-dark-gray);
  font-size: 0.9rem;                        /* keep compact text like nav */
  font-weight: var(--360t-font-medium);
  font-family: inherit;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 56px;                          /* compact width */
  justify-content: center;
  border-radius: var(--360t-radius-lg);
}

.toggle-button:hover {
  background-color: var(--360t-light-gray);
  color: var(--360t-text);
  border-color: var(--360t-primary);
}

.toggle-button.active {
  background-color: var(--360t-primary);
  color: var(--360t-white);
  font-weight: var(--360t-font-semibold);
  box-shadow: var(--360t-shadow-md);
  border-color: var(--360t-primary);
  height: 36px; /* ensure active state maintains the same height */
}

.toggle-button.active:hover {
  background-color: var(--360t-primary-dark);
}

.toggle-icon {
  color: currentColor;
  flex-shrink: 0;
  width: 16px;
  height: 16px; /* slightly smaller icon to fit compact height nicely */
}

/* Secondary Actions */
.secondary-actions {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  flex-shrink: 0;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--360t-radius-lg);
  border: 1px solid var(--360t-primary);
  background-color: var(--360t-primary); /* green background */
  color: var(--360t-white);               /* white text (for any text variant) */
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-button:hover {
  background-color: var(--360t-primary-dark); /* darker green on hover */
  border-color: var(--360t-primary-dark);
  color: var(--360t-white);
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
}

.action-button:active {
  transform: translateY(0);
  box-shadow: var(--360t-shadow-sm);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: var(--360t-space-2);
  gap: var(--360t-space-1);
}

.menu-line {
  width: 20px;
  height: 2px;
  background-color: var(--360t-dark-gray);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.menu-line.open:nth-child(1) {
  transform: rotate(45deg) translate(3px, 3px);
}

.menu-line.open:nth-child(2) {
  opacity: 0;
}

.menu-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(3px, -3px);
}


/* Mobile Menu Overlay */
.mobile-menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

/* Responsive Design */
@media (max-width: 1200px) {
  /* Zoom-friendly compaction */
  .header-nav {
    max-width: none;
    width: 100%;
  }

  .nav-list {
    gap: var(--360t-space-1);
    padding: 4px 6px;
  }

  .nav-button {
    padding: 4px 7px;
    max-width: 130px;
    min-height: 34px;
    font-size: 0.88rem;
  }

  .nav-text {
    max-width: 108px;
  }

  .view-mode-toggle .toggle-label {
    display: none;  /* reduce right cluster width */
  }

  .view-mode-toggle {
    padding: var(--360t-space-2);
  }
}

@media (max-width: 1024px) {
  /* Further compaction when viewport (or zoomed viewport) is tighter */
  .header-nav {
    max-width: none;
    width: 100%;
  }
  .nav-button {
    padding: 4px 6px;
    max-width: 124px;
    min-height: 32px;
    font-size: 0.86rem;
  }
  .nav-text {
    max-width: 100px;
  }
}
@media (max-width: 768px) {
  /* Maintain the grid even on mobile to avoid stacking overlap */
  .modern-header {
    grid-template-columns: auto 1fr auto;
  }
  .header-main {
    padding: var(--360t-space-3) var(--360t-space-4);
  }

  .brand-title {
    font-size: var(--360t-text-lg);
  }

  /* Mobile menu behavior with consistent header height */
  .header-nav {
    position: fixed;
    top: var(--header-height, 96px);  /* FIXED: Use correct variable and realistic fallback */
    left: 0;
    right: 0;
    background-color: var(--360t-white);
    border-bottom: 1px solid var(--360t-border);
    box-shadow: var(--360t-shadow-lg);
    transform: translateY(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 950;  /* FIXED: Higher z-index for mobile overlay */
    padding: var(--360t-space-4);
    max-width: none;
  }

  .header-nav.nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    background-color: transparent;
    padding: 0;
    gap: var(--360t-space-2);
  }

  .nav-button {
    width: 100%;
    justify-content: flex-start;
    padding: var(--360t-space-4);
    border-radius: var(--360t-radius-lg);
    background-color: var(--360t-light-gray);
  }

  .nav-button.active::after {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .mobile-menu-overlay {
    display: block;
  }

  .view-mode-toggle {
    order: -1;
  }
}

@media (max-width: 480px) {
  .header-main {
    padding: var(--360t-space-2) var(--360t-space-3);
  }

  .brand-text {
    display: none;
  }

  .brand-logo {
    height: 36px;
  }

  .secondary-actions {
    gap: var(--360t-space-1);
  }

  .action-button {
    width: 36px;
    height: 36px;
  }

  .view-mode-toggle {
    padding: var(--360t-space-1) var(--360t-space-2);
  }

  .toggle-button {
    padding: var(--360t-space-1) var(--360t-space-2);
    min-width: 44px;
  }

}

/* Focus and Accessibility */
.nav-button:focus-visible,
.toggle-button:focus-visible,
.action-button:focus-visible,
.mobile-menu-toggle:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-header {
    border-bottom-width: 2px;
  }

  .nav-button.active::after {
    height: 3px;
  }

  .toggle-button.active {
    border: 2px solid var(--360t-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .nav-button,
  .toggle-button,
  .action-button,
  .menu-line,
  .header-nav {
    transition: none;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .modern-header {
    /* Dark mode styles can be added here when needed */
  }
}
