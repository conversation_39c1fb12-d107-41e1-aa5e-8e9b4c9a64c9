import { test, expect } from '@playwright/test';

test.describe('Streaming Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the chat interface
    await page.goto('http://localhost:5178/?view=chat');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Enable streaming if not already enabled
    const streamingToggle = page.locator('input[type="checkbox"]');
    if (await streamingToggle.isVisible()) {
      const isChecked = await streamingToggle.isChecked();
      if (!isChecked) {
        await streamingToggle.check();
        console.log('✅ Streaming enabled');
      }
    }
    
    // Wait for chat interface to be ready
    await page.waitForTimeout(2000);
  });

  test('should successfully send message and receive streaming response', async ({ page }) => {
    console.log('🧪 Testing streaming functionality...');
    
    // Take initial screenshot
    await page.screenshot({ path: 'test-results/streaming-initial.png' });
    
    // Send a test query
    await page.fill('input[type="text"], textarea', 'what is a FWD');
    await page.keyboard.press('Enter');
    
    console.log('📤 Query sent, waiting for response...');
    
    // Wait for response to appear
    await page.waitForTimeout(8000);
    
    // Take screenshot after response
    await page.screenshot({ path: 'test-results/streaming-response.png' });
    
    // Get page content to analyze
    const pageContent = await page.textContent('body');
    console.log('📄 Page content length:', pageContent?.length);
    
    // Check if we got any response content
    const hasContent = pageContent && pageContent.length > 500; // Should have substantial content
    console.log('📊 Content check:', { length: pageContent?.length, hasContent });
    expect(hasContent).toBeTruthy();
    
    // Look for key indicators of successful streaming
    const indicators = {
      hasUserMessage: pageContent?.includes('what is a FWD') || false,
      hasResponseContent: pageContent && pageContent.length > 500, // Substantial response
      noRawJSON: !pageContent?.includes('{"type":') && !pageContent?.includes('"path":'),
      hasStructuredElements: pageContent?.includes('entity') || pageContent?.includes('reference') || false
    };
    
    console.log('📊 Response indicators:', indicators);
    
    // Validate core functionality
    expect(indicators.hasUserMessage).toBeTruthy();
    expect(indicators.hasResponseContent).toBeTruthy();
    expect(indicators.noRawJSON).toBeTruthy();
    
    console.log('✅ Streaming validation passed');
  });

  test('should handle multiple queries correctly', async ({ page }) => {
    console.log('🧪 Testing multiple queries...');
    
    // Send first query
    await page.fill('input[type="text"], textarea', 'what is a FWD');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(8000);
    
    // Send second query
    await page.fill('input[type="text"], textarea', 'what is a swap');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(8000);
    
    // Check that we have content from both queries
    const pageContent = await page.textContent('body');
    const hasFirstQuery = pageContent?.includes('FWD') || false;
    const hasSecondQuery = pageContent?.includes('swap') || false;
    
    console.log('📊 Multiple query results:', { hasFirstQuery, hasSecondQuery });
    
    expect(hasFirstQuery).toBeTruthy();
    expect(hasSecondQuery).toBeTruthy();
    
    console.log('✅ Multiple query test passed');
  });
});
