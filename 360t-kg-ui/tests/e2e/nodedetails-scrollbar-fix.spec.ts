import { test, expect } from '@playwright/test';

test.describe('NodeDetails Scrollbar Fix Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5178');
    
    // Wait for the application to load
    await page.waitForSelector('.graph-container', { timeout: 10000 });
    
    // Wait for graph data to load
    await page.waitForFunction(() => {
      const nodes = document.querySelectorAll('.force-graph-container canvas, .force-graph-container svg');
      return nodes.length > 0;
    }, { timeout: 15000 });
  });

  test('should display scrollbar in NodeDetails panel when content overflows', async ({ page }) => {
    console.log('🧪 Testing NodeDetails scrollbar functionality...');

    // Navigate directly to a URL with a node selected (if available)
    // Or try to trigger node selection through graph interaction
    console.log('🔍 Looking for graph nodes to select...');

    // Wait for graph to be fully loaded
    await page.waitForTimeout(3000);

    // Try to find and click on a graph node directly
    const graphCanvas = page.locator('canvas').first();
    if (await graphCanvas.count() > 0) {
      console.log('📊 Found graph canvas, attempting to click on nodes...');

      // Click in multiple positions to try to hit a node
      const positions = [
        { x: 200, y: 200 },
        { x: 300, y: 300 },
        { x: 400, y: 250 },
        { x: 150, y: 350 },
        { x: 500, y: 200 }
      ];

      for (const pos of positions) {
        await graphCanvas.click({ position: pos });
        await page.waitForTimeout(500);

        // Check if NodeDetails panel appeared
        const nodeDetailsPanel = page.locator('.node-details-modern, .details-panel');
        if (await nodeDetailsPanel.isVisible()) {
          console.log(`✅ NodeDetails panel opened after clicking at position ${pos.x}, ${pos.y}`);
          break;
        }
      }
    }
    
    // Wait for NodeDetails panel to appear
    console.log('⏳ Waiting for NodeDetails panel...');
    const nodeDetailsPanel = page.locator('.node-details-modern, .details-panel');

    // If panel is not visible, try to trigger it programmatically
    if (!(await nodeDetailsPanel.isVisible())) {
      console.log('🔧 Panel not visible, trying to trigger it programmatically...');

      // Try to trigger node selection through JavaScript
      await page.evaluate(() => {
        // Look for any available node data and trigger selection
        const event = new CustomEvent('nodeSelect', {
          detail: {
            id: 'test-node',
            name: 'Test Node',
            properties: {
              name: 'Test Node for Scrollbar Testing',
              summary: 'This is a test node with a long summary that should create enough content to test scrollbar functionality. '.repeat(10),
              description: 'This is a detailed description that adds more content to the panel. '.repeat(15)
            }
          }
        });
        window.dispatchEvent(event);
      });

      await page.waitForTimeout(1000);
    }

    await expect(nodeDetailsPanel).toBeVisible({ timeout: 5000 });
    
    console.log('✅ NodeDetails panel is visible');
    
    // Check if the panel has the correct structure
    const panelHeader = page.locator('.node-details-header');
    const panelContent = page.locator('.node-details-content');
    
    await expect(panelHeader).toBeVisible();
    await expect(panelContent).toBeVisible();
    
    console.log('✅ Panel header and content are visible');
    
    // Take a screenshot of the panel
    await page.screenshot({ 
      path: 'test-results/nodedetails-scrollbar-before.png',
      fullPage: true 
    });
    
    // Check CSS properties of the panel and content
    const panelStyles = await panelContent.evaluate((el) => {
      const styles = window.getComputedStyle(el);
      return {
        overflowY: styles.overflowY,
        overflowX: styles.overflowX,
        height: styles.height,
        maxHeight: styles.maxHeight,
        flex: styles.flex,
        minHeight: styles.minHeight,
        scrollbarWidth: styles.scrollbarWidth,
        scrollbarGutter: styles.scrollbarGutter
      };
    });
    
    console.log('📊 Panel content styles:', panelStyles);
    
    // Verify the CSS properties are correct
    expect(panelStyles.overflowY).toBe('auto');
    expect(panelStyles.overflowX).toBe('hidden');
    expect(panelStyles.flex).toContain('1');
    expect(panelStyles.minHeight).toBe('0px');
    
    // Check if content is scrollable by measuring scroll height vs client height
    const scrollInfo = await panelContent.evaluate((el) => {
      return {
        scrollHeight: el.scrollHeight,
        clientHeight: el.clientHeight,
        offsetHeight: el.offsetHeight,
        hasVerticalScrollbar: el.scrollHeight > el.clientHeight,
        scrollTop: el.scrollTop,
        scrollTopMax: el.scrollHeight - el.clientHeight
      };
    });
    
    console.log('📏 Scroll measurements:', scrollInfo);
    
    // If content is scrollable, test scrolling
    if (scrollInfo.hasVerticalScrollbar) {
      console.log('✅ Content is scrollable - testing scroll functionality...');
      
      // Scroll down
      await panelContent.evaluate((el) => {
        el.scrollTop = el.scrollHeight / 2;
      });
      
      await page.waitForTimeout(500);
      
      // Verify scroll position changed
      const newScrollTop = await panelContent.evaluate((el) => el.scrollTop);
      expect(newScrollTop).toBeGreaterThan(0);
      
      console.log('✅ Scrolling works correctly');
      
      // Take screenshot after scrolling
      await page.screenshot({ 
        path: 'test-results/nodedetails-scrollbar-after-scroll.png',
        fullPage: true 
      });
    } else {
      console.log('ℹ️ Content fits within panel height - no scrollbar needed');
      
      // Try to expand sections to create more content
      const expandButtons = page.locator('.expand-icon-card, .section-header-card[role="button"]');
      const expandButtonCount = await expandButtons.count();
      
      if (expandButtonCount > 0) {
        console.log(`🔄 Found ${expandButtonCount} expandable sections, expanding them...`);
        
        for (let i = 0; i < Math.min(expandButtonCount, 3); i++) {
          await expandButtons.nth(i).click();
          await page.waitForTimeout(300);
        }
        
        // Re-check scroll after expanding
        const newScrollInfo = await panelContent.evaluate((el) => {
          return {
            scrollHeight: el.scrollHeight,
            clientHeight: el.clientHeight,
            hasVerticalScrollbar: el.scrollHeight > el.clientHeight
          };
        });
        
        console.log('📏 Scroll measurements after expansion:', newScrollInfo);
        
        if (newScrollInfo.hasVerticalScrollbar) {
          console.log('✅ Scrollbar appeared after expanding content');
          
          // Test scrolling
          await panelContent.evaluate((el) => {
            el.scrollTop = 100;
          });
          
          const scrollTop = await panelContent.evaluate((el) => el.scrollTop);
          expect(scrollTop).toBeGreaterThan(0);
          
          console.log('✅ Scrolling works after expansion');
        }
      }
    }
    
    // Final screenshot
    await page.screenshot({ 
      path: 'test-results/nodedetails-scrollbar-final.png',
      fullPage: true 
    });
    
    console.log('✅ NodeDetails scrollbar test completed successfully');
  });

  test('should maintain sticky header while scrolling', async ({ page }) => {
    console.log('🧪 Testing sticky header behavior...');

    // Try to find and click on a graph node
    await page.waitForTimeout(3000);

    const graphCanvas = page.locator('canvas').first();
    if (await graphCanvas.count() > 0) {
      const positions = [
        { x: 200, y: 200 },
        { x: 300, y: 300 },
        { x: 400, y: 250 }
      ];

      for (const pos of positions) {
        await graphCanvas.click({ position: pos });
        await page.waitForTimeout(500);

        const nodeDetailsPanel = page.locator('.node-details-modern, .details-panel');
        if (await nodeDetailsPanel.isVisible()) {
          break;
        }
      }
    }
    
    const nodeDetailsPanel = page.locator('.node-details-modern, .details-panel');
    await expect(nodeDetailsPanel).toBeVisible({ timeout: 10000 });
    
    const panelHeader = page.locator('.node-details-header');
    const panelContent = page.locator('.node-details-content');
    
    // Check header CSS properties
    const headerStyles = await panelHeader.evaluate((el) => {
      const styles = window.getComputedStyle(el);
      return {
        position: styles.position,
        top: styles.top,
        zIndex: styles.zIndex,
        flexShrink: styles.flexShrink
      };
    });
    
    console.log('📊 Header styles:', headerStyles);
    
    // Verify sticky positioning
    expect(headerStyles.position).toBe('sticky');
    expect(headerStyles.top).toBe('0px');
    expect(headerStyles.flexShrink).toBe('0');
    
    console.log('✅ Header has correct sticky positioning');
  });
});
