import { test, expect } from '@playwright/test';

test.describe('Streaming Chat Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the chat interface
    await page.goto('http://localhost:5178/?view=chat');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Enable streaming if not already enabled
    const streamingToggle = page.locator('input[type="checkbox"]');
    if (await streamingToggle.isVisible()) {
      const isChecked = await streamingToggle.isChecked();
      if (!isChecked) {
        await streamingToggle.check();
        console.log('✅ Streaming enabled');
      }
    }

    // Wait for chat interface to be ready
    await page.waitForTimeout(2000);
  });

  test('should display thinking text in real-time without raw JSON', async ({ page }) => {
    console.log('🧪 Starting comprehensive streaming test...');

    // Send a test query
    await page.fill('input[type="text"], textarea', 'what is a FWD');
    await page.keyboard.press('Enter');

    console.log('📤 Query sent, waiting for streaming response...');

    // Wait for streaming to start and thinking content to appear
    await page.waitForTimeout(3000);

    // Look for thinking section
    const thinkingSection = page.locator('.thinking-section, [class*="thinking"]');
    const hasThinkingSection = await thinkingSection.count() > 0;

    if (hasThinkingSection) {
      console.log('🧠 Found thinking section, checking content...');
      const thinkingContent = await thinkingSection.textContent();

      // Verify thinking content is NOT raw JSON
      expect(thinkingContent).not.toContain('{"type":');
      expect(thinkingContent).not.toContain('"path":');
      expect(thinkingContent).not.toContain('"value":');

      console.log('✅ Thinking content is properly formatted (not raw JSON)');
    } else {
      console.log('ℹ️ No thinking section found - this may be expected for some responses');
    }

    // Wait for final response
    await page.waitForTimeout(10000);

    // Check for structured response elements
    const responseElements = {
      entityBadges: await page.locator('.entity-badge, [class*="entity"]').count(),
      references: await page.locator('.reference, [class*="reference"], .source').count(),
      structuredContent: await page.locator('.structured-response, [class*="structured"]').count()
    };

    console.log('📊 Response elements found:', responseElements);

    // Verify we got a complete response
    const messages = await page.locator('article').count();
    expect(messages).toBeGreaterThanOrEqual(2); // User + Assistant

    console.log('✅ Comprehensive streaming test passed');
  });

  test('should show only ONE response message box', async ({ page }) => {
    console.log('🧪 Testing single response message box...');
    
    // Send a test query
    const inputField = page.getByRole('textbox', { name: 'Type your message here' });
    await inputField.fill('what is a FWD');

    const submitButton = page.getByRole('button', { name: 'Send message' });
    await submitButton.click();
    
    // Wait for streaming to start
    await page.waitForTimeout(2000);
    
    // Count assistant message bubbles during streaming
    const assistantMessages = page.locator('.message-bubble.assistant');
    const messageCount = await assistantMessages.count();
    
    console.log(`📊 Found ${messageCount} assistant message bubbles during streaming`);
    
    // During streaming, there should be 0 assistant message bubbles (hidden)
    // or at most 1 if streaming has completed
    expect(messageCount).toBeLessThanOrEqual(1);
    
    // Wait for streaming to complete
    await page.waitForFunction(() => {
      const thinkingPanel = document.querySelector('.thinking-panel, [class*="thinking"]');
      return !thinkingPanel || !thinkingPanel.textContent!.trim();
    }, { timeout: 30000 });
    
    // After streaming completes, there should be exactly 1 assistant message
    const finalMessageCount = await assistantMessages.count();
    console.log(`📊 Found ${finalMessageCount} assistant message bubbles after streaming`);
    expect(finalMessageCount).toBe(1);
    
    console.log('✅ Single message box validation passed');
  });

  test('should display structured response after streaming completes', async ({ page }) => {
    console.log('🧪 Testing structured response after streaming...');
    
    // Send a test query
    const inputField = page.getByRole('textbox', { name: 'Type your message here' });
    await inputField.fill('what is a FWD');

    const submitButton = page.getByRole('button', { name: 'Send message' });
    await submitButton.click();
    
    console.log('📤 Query sent, waiting for streaming to complete...');
    
    // Wait for streaming to complete (thinking panel disappears or becomes inactive)
    await page.waitForFunction(() => {
      const thinkingPanel = document.querySelector('.thinking-panel, [class*="thinking"]');
      return !thinkingPanel || !thinkingPanel.textContent!.trim() || 
             thinkingPanel.style.display === 'none' ||
             !thinkingPanel.classList.contains('active');
    }, { timeout: 45000 });
    
    console.log('🏁 Streaming completed, checking final response...');
    
    // Wait for structured response elements to appear
    const structuredResponse = page.locator('.structured-response, [class*="structured"]');
    await expect(structuredResponse).toBeVisible({ timeout: 10000 });
    
    // Check for key structured response elements
    const responseElements = {
      sources: page.locator('[class*="source"], [class*="reference"]'),
      entities: page.locator('[class*="entity"], [class*="badge"]'),
      followUp: page.locator('[class*="follow"], [class*="suggestion"]')
    };
    
    // At least one of these should be present in a structured response
    let hasStructuredElements = false;
    for (const [elementType, locator] of Object.entries(responseElements)) {
      const count = await locator.count();
      console.log(`📊 Found ${count} ${elementType} elements`);
      if (count > 0) {
        hasStructuredElements = true;
      }
    }
    
    // Verify the response is not just raw JSON
    const messageContent = await page.locator('.message-bubble.assistant').textContent();
    expect(messageContent).not.toContain('{"answer":');
    expect(messageContent).not.toContain('"metadata":');
    
    console.log('✅ Structured response validation passed');
    console.log('📋 Has structured elements:', hasStructuredElements);
  });

  test('should handle streaming errors gracefully', async ({ page }) => {
    console.log('🧪 Testing error handling...');
    
    // Monitor console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Send a test query
    const inputField = page.getByRole('textbox', { name: 'Type your message here' });
    await inputField.fill('test error handling');

    const submitButton = page.getByRole('button', { name: 'Send message' });
    await submitButton.click();
    
    // Wait for response (success or error)
    await page.waitForTimeout(10000);
    
    // Check that no critical errors occurred
    const criticalErrors = consoleErrors.filter(error => 
      error.includes('TypeError') || 
      error.includes('ReferenceError') ||
      error.includes('applySseDeltaThinking')
    );
    
    console.log('🚨 Console errors found:', consoleErrors.length);
    console.log('🚨 Critical errors found:', criticalErrors.length);
    
    if (criticalErrors.length > 0) {
      console.log('🚨 Critical errors:', criticalErrors);
    }
    
    expect(criticalErrors.length).toBe(0);
    
    console.log('✅ Error handling validation passed');
  });
});
