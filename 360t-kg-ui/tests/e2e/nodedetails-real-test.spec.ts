import { test, expect } from '@playwright/test';

test.describe('Real NodeDetails Scrollbar Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:5178');
    await page.waitForSelector('.graph-container', { timeout: 10000 });
    await page.waitForTimeout(3000); // Wait for graph to load
  });

  test('should validate NodeDetails scrollbar in real application', async ({ page }) => {
    console.log('🧪 Testing real NodeDetails scrollbar...');
    
    // Try to open NodeDetails by simulating a node selection
    // First, let's check if there's a way to programmatically select a node
    const hasNodes = await page.evaluate(() => {
      // Check if there are any nodes available in the graph
      const graphData = window.graphData || window.nodes || [];
      return Array.isArray(graphData) && graphData.length > 0;
    });
    
    if (hasNodes) {
      console.log('📊 Found graph data, attempting to select a node...');
      
      // Try to trigger node selection programmatically
      await page.evaluate(() => {
        // Look for graph instance or node selection methods
        if (window.selectNode && typeof window.selectNode === 'function') {
          const nodes = window.graphData || window.nodes || [];
          if (nodes.length > 0) {
            window.selectNode(nodes[0]);
          }
        }
      });
    }
    
    // Alternative: Try clicking on the graph canvas
    const canvas = page.locator('canvas').first();
    if (await canvas.count() > 0) {
      console.log('🖱️ Trying to click on graph canvas...');
      
      // Try multiple click positions
      const positions = [
        { x: 300, y: 200 },
        { x: 400, y: 300 },
        { x: 200, y: 250 },
        { x: 500, y: 200 },
        { x: 350, y: 350 }
      ];
      
      for (const pos of positions) {
        await canvas.click({ position: pos });
        await page.waitForTimeout(800);
        
        // Check if NodeDetails panel appeared
        const panel = page.locator('.node-details-modern');
        if (await panel.isVisible()) {
          console.log(`✅ NodeDetails panel opened at position ${pos.x}, ${pos.y}`);
          break;
        }
      }
    }
    
    // Check if panel is now visible
    const nodeDetailsPanel = page.locator('.node-details-modern');
    const isPanelVisible = await nodeDetailsPanel.isVisible();
    
    if (isPanelVisible) {
      console.log('✅ NodeDetails panel is visible, testing scrollbar...');
      
      // Test the scrollbar implementation
      const panelContent = page.locator('.node-details-content');
      
      // Verify CSS properties
      const contentStyles = await panelContent.evaluate((el) => {
        const styles = window.getComputedStyle(el);
        return {
          overflowY: styles.overflowY,
          overflowX: styles.overflowX,
          flex: styles.flex,
          minHeight: styles.minHeight
        };
      });
      
      console.log('📊 Real panel content styles:', contentStyles);
      
      expect(contentStyles.overflowY).toBe('auto');
      expect(contentStyles.overflowX).toBe('hidden');
      expect(contentStyles.flex).toContain('1');
      expect(contentStyles.minHeight).toBe('0px');
      
      // Check scroll behavior
      const scrollInfo = await panelContent.evaluate((el) => {
        return {
          scrollHeight: el.scrollHeight,
          clientHeight: el.clientHeight,
          hasScrollbar: el.scrollHeight > el.clientHeight
        };
      });
      
      console.log('📏 Real panel scroll info:', scrollInfo);
      
      if (scrollInfo.hasScrollbar) {
        console.log('✅ Real panel has scrollbar - testing scroll functionality...');
        
        // Test scrolling
        await panelContent.evaluate((el) => {
          el.scrollTop = 50;
        });
        
        await page.waitForTimeout(200);
        
        const scrollTop = await panelContent.evaluate((el) => el.scrollTop);
        console.log('📍 Real panel scroll position:', scrollTop);
        
        if (scrollTop > 0) {
          console.log('✅ Real panel scrolling works correctly');
        }
      } else {
        console.log('ℹ️ Real panel content fits within height - no scrollbar needed');
      }
      
      // Take screenshot of real panel
      await page.screenshot({ 
        path: 'test-results/real-nodedetails-scrollbar.png',
        fullPage: true 
      });
      
    } else {
      console.log('⚠️ Could not open NodeDetails panel in real application');
      console.log('ℹ️ This may be due to no nodes being available or graph not fully loaded');
      
      // Still validate that the CSS is loaded correctly
      const cssExists = await page.evaluate(() => {
        const styles = Array.from(document.styleSheets);
        return styles.some(sheet => {
          try {
            const rules = Array.from(sheet.cssRules || sheet.rules || []);
            return rules.some(rule => 
              rule.selectorText && rule.selectorText.includes('.node-details-content')
            );
          } catch (e) {
            return false;
          }
        });
      });
      
      console.log('📋 NodeDetails CSS loaded:', cssExists);
      expect(cssExists).toBe(true);
    }
    
    console.log('✅ Real NodeDetails test completed');
  });
});
