# Knowledge Graph Visualizer - AI Layer Documentation

A sophisticated FastAPI-based AI service featuring advanced LLM provider abstraction, intelligent failover mechanisms, and comprehensive conversation management for the Knowledge Graph Visualizer platform.

## 🧠 AI Layer Architecture Overview

### LLM Provider Abstraction Framework
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ AI Layer - FastAPI Service (Port 8000)                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ FastAPI Server  │ │ LLM Manager     │ │ Context Builder │               │
│ │ • Async Endpoints• Provider Pool   │ │ • Neo4j Queries │               │
│ │ • Error Handling│ • Load Balancing │ │ • Knowledge Base│               │
│ │ • Middleware    │ • Health Checks  │ │ • Context Window│               │
│ │ • Validation    │ • Circuit Breaker│ │ • Optimization  │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ Provider Mgmt   │ │ Conversation    │ │ Enhanced Error  │               │
│ │ • OpenAI GPT    │ │ • History Mgmt  │ │ • Retry Logic   │               │
│ │ • Google Gemini │ │ • Session State │ │ • Fallback      │               │
│ │ • Ollama Local  │ │ • Persistence   │ │ • Recovery      │               │
│ │ • Azure OpenAI  │ │ • Context Sync  │ │ • Monitoring    │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
└─────────────────────────────────────────────────────────────────────────────┘
                                  │
                    Multi-Provider LLM Integration
                                  │
┌─────────────────────────────────────────────────────────────────────────────┐
│ LLM Provider Ecosystem                                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ OpenAI GPT      │ │ Google Gemini   │ │ Local Ollama    │               │
│ │ • GPT-4 Turbo   │ │ • Gemini Pro    │ │ • Llama 2/3     │               │
│ │ • GPT-3.5 Turbo │ │ • Gemini Flash  │ │ • Code Llama    │               │
│ │ • Fast Response │ │ • Free Tier     │ │ • Mistral       │               │
│ │ • High Quality  │ │ • Multimodal    │ │ • Privacy       │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ Azure OpenAI    │ │ Provider Health │ │ Smart Fallback  │               │
│ │ • Enterprise    │ │ • Availability  │ │ • Auto-switch   │               │
│ │ • Compliance    │ │ • Performance   │ │ • Load Balance  │               │
│ │ • Custom Deploy │ │ • Rate Limits   │ │ • Circuit Break │               │
│ │ • Security      │ │ • Error Rates   │ │ • Recovery      │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 🔄 Advanced Provider Selection & Failover

### Intelligent Provider Selection Matrix
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Provider Selection Strategy Decision Tree                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ Chat Request → Provider Selector → Selection Algorithm                      │
│                        │                                                    │
│               ┌────────┴────────┐                                          │
│               │                 │                                          │
│          User Preference    System Intelligence                             │
│               │                 │                                          │
│               ▼                 ▼                                          │
│    ┌─────────────────┐ ┌─────────────────┐                                │
│    │ Explicit Choice │ │ Smart Selection │                                │
│    │ • User Setting  │ │ • Performance   │                                │
│    │ • API Param     │ │ • Availability  │                                │
│    │ • Override      │ │ • Load Balance  │                                │
│    └─────────────────┘ └─────────────────┘                                │
│               │                 │                                          │
│               └────────┬────────┘                                          │
│                        │                                                    │
│                        ▼                                                    │
│              ┌─────────────────┐                                           │
│              │ Health Check    │                                           │
│              │ • Availability  │                                           │
│              │ • Response Time │                                           │
│              │ • Error Rate    │                                           │
│              │ • Rate Limits   │                                           │
│              └─────────────────┘                                           │
│                        │                                                    │
│        ┌───────────────┼───────────────┐                                  │
│        │               │               │                                  │
│   ✅ Healthy      ⚠️ Degraded     ❌ Unhealthy                           │
│        │               │               │                                  │
│        ▼               ▼               ▼                                  │
│  Use Selected    Try Fallback   Circuit Breaker                          │
│    Provider       Provider       (Use Last Good)                         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Provider Failover Flow Chart
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Advanced Failover Strategy with Circuit Breaker Pattern                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ Request → Primary Provider                                                  │
│              │                                                              │
│              ▼                                                              │
│    ┌─────────────────┐                                                     │
│    │ Provider Health │                                                     │
│    │ Check           │                                                     │
│    └─────────────────┘                                                     │
│              │                                                              │
│        ┌─────┴─────┐                                                       │
│        │           │                                                       │
│    🟢 Healthy   🔴 Failed                                                 │
│        │           │                                                       │
│        ▼           ▼                                                       │
│    Execute    ┌─────────────────┐                                         │
│    Request    │ Increment       │                                         │
│        │      │ Failure Counter │                                         │
│        │      └─────────────────┘                                         │
│        │           │                                                       │
│        │           ▼                                                       │
│        │    ┌─────────────────┐         ┌─────────────────┐              │
│        │    │ Failure Count   │   Yes   │ Circuit Breaker │              │
│        │    │ > Threshold?    │────────▶│ OPEN            │              │
│        │    │ (Default: 3)    │         │ (Skip Provider) │              │
│        │    └─────────────────┘         └─────────────────┘              │
│        │           │ No                          │                        │
│        │           ▼                             │                        │
│        │    ┌─────────────────┐                 │                        │
│        │    │ Try Next        │                 │                        │
│        │    │ Provider in     │                 │                        │
│        │    │ Fallback Chain  │                 │                        │
│        │    └─────────────────┘                 │                        │
│        │           │                             │                        │
│        │           └─────────────────────────────┘                        │
│        │                       │                                          │
│        └───────────────────────┼──────────────────────────────────────┐   │
│                                │                                      │   │
│                                ▼                                      │   │
│                      ┌─────────────────┐                            │   │
│                      │ All Providers   │          Yes               │   │
│                      │ Failed?         │───────────────────────────┐│   │
│                      └─────────────────┘                           ││   │
│                                │ No                                 ││   │
│                                ▼                                    ││   │
│                      ┌─────────────────┐                           ││   │
│                      │ Return Success  │                           ││   │
│                      │ Response        │                           ││   │
│                      └─────────────────┘                           ││   │
│                                                                     ││   │
│                                                                     ▼▼   │
│                                                           ┌─────────────────┐
│                                                           │ Return Error    │
│                                                           │ with Fallback  │
│                                                           │ Message         │
│                                                           └─────────────────┘
└─────────────────────────────────────────────────────────────────────────────┘
```

## 🏗️ LLM Provider Architecture Details

### Provider Implementation Matrix
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Comprehensive Provider Capabilities Matrix                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐   │
│ │ OpenAI Provider │ Google Provider │ Ollama Provider │ Azure Provider  │   │
│ ├─────────────────┼─────────────────┼─────────────────┼─────────────────┤   │
│ │ 🔹 Features     │ 🔹 Features     │ 🔹 Features     │ 🔹 Features     │   │
│ │ • GPT-4 Turbo   │ • Gemini Pro    │ • Local Models  │ • Enterprise    │   │
│ │ • GPT-3.5 Turbo │ • Gemini Flash  │ • Llama 2/3     │ • Compliance    │   │
│ │ • Function Call │ • Multimodal    │ • Code Llama    │ • Custom Deploy │   │
│ │ • JSON Mode     │ • Free Tier     │ • Mistral       │ • Security      │   │
│ │ • Streaming     │ • Long Context  │ • Privacy First │ • SLA Support   │   │
│ ├─────────────────┼─────────────────┼─────────────────┼─────────────────┤   │
│ │ 🔸 Limits       │ 🔸 Limits       │ 🔸 Limits       │ 🔸 Limits       │   │
│ │ • Rate Limits   │ • Rate Limits   │ • Hardware      │ • Custom Limits │   │
│ │ • Token Costs   │ • Request Quota │ • Model Size    │ • Deployment    │   │
│ │ • Context Size  │ • Context Size  │ • Local CPU/GPU │ • Regional      │   │
│ ├─────────────────┼─────────────────┼─────────────────┼─────────────────┤   │
│ │ 🔺 Performance  │ 🔺 Performance  │ 🔺 Performance  │ 🔺 Performance  │   │
│ │ • Fast (1-3s)   │ • Very Fast     │ • Variable      │ • Fast (1-4s)   │   │
│ │ • Reliable      │ • Consistent    │ • Hardware Dep  │ • Consistent    │   │
│ │ • Global CDN    │ • Global Access │ • Local Network │ • Regional      │   │
│ └─────────────────┴─────────────────┴─────────────────┴─────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Context Window Management System
```javascript
// Advanced context window optimization
const contextWindowManager = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Provider-Specific Context Limits                               │
  // └─────────────────────────────────────────────────────────────────┘
  providerLimits: {
    'openai': {
      'gpt-4-turbo': { max: 128000, safe: 120000, reserve: 8000 },
      'gpt-3.5-turbo': { max: 16385, safe: 15000, reserve: 1385 }
    },
    'google': {
      'gemini-pro': { max: 30720, safe: 28000, reserve: 2720 },
      'gemini-flash': { max: 1048576, safe: 1000000, reserve: 48576 }
    },
    'ollama': {
      'llama2': { max: 4096, safe: 3500, reserve: 596 },
      'codellama': { max: 16384, safe: 15000, reserve: 1384 }
    },
    'azure': {
      'gpt-4': { max: 32768, safe: 30000, reserve: 2768 }
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Smart Context Management Strategies                             │
  // └─────────────────────────────────────────────────────────────────┘
  managementStrategies: {
    // Prioritize recent messages
    prioritizeRecent: {
      keepMostRecent: 5,          // Always keep last 5 messages
      compressOlder: true,        // Compress older messages
      summarizeThreshold: 20      // Summarize if > 20 messages
    },

    // Graph context optimization
    graphContextOptimization: {
      maxNodes: 50,               // Limit graph context nodes
      maxRelationships: 100,      // Limit relationship context
      prioritizeRelevant: true,   // Prioritize relevant entities
      includeNeighbors: 2         // Include 2-hop neighbors
    },

    // Adaptive compression
    adaptiveCompression: {
      enableCompression: true,    // Enable context compression
      compressionRatio: 0.3,      // Target 30% compression
      preserveImportant: true,    // Preserve important information
      compressionAlgorithm: 'semantic' // Semantic-aware compression
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Context Building Pipeline                                       │
  // └─────────────────────────────────────────────────────────────────┘
  async buildOptimalContext(conversationHistory, graphContext, provider, model) {
    const limits = this.providerLimits[provider][model];
    let context = [];

    // 1. Add system prompt (always included)
    context.push({
      role: 'system',
      content: this.buildSystemPrompt(graphContext),
      tokens: this.estimateTokens(this.buildSystemPrompt(graphContext))
    });

    // 2. Add graph context (condensed if needed)
    const graphContextContent = this.buildGraphContext(graphContext);
    context.push({
      role: 'system', 
      content: graphContextContent,
      tokens: this.estimateTokens(graphContextContent)
    });

    // 3. Add conversation history (with smart truncation)
    const processedHistory = this.optimizeConversationHistory(
      conversationHistory, 
      limits.safe - this.getTotalTokens(context) - limits.reserve
    );
    context.push(...processedHistory);

    // 4. Validate final context size
    const totalTokens = this.getTotalTokens(context);
    if (totalTokens > limits.safe) {
      context = this.emergencyTruncation(context, limits.safe);
    }

    return {
      context,
      tokenCount: this.getTotalTokens(context),
      compressionRatio: this.calculateCompressionRatio(conversationHistory, context),
      truncated: totalTokens > limits.safe
    };
  }
};
```

## 🔄 Conversation Management Architecture

### Advanced Conversation State Management
```javascript
// Sophisticated conversation handling system
const conversationManager = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Multi-Layer Conversation Persistence                           │
  // └─────────────────────────────────────────────────────────────────┘
  persistenceLayers: {
    // Memory cache for active conversations
    memoryCache: {
      type: 'LRU',
      maxSize: 1000,              // Max 1000 active conversations
      ttl: 3600000,               // 1 hour TTL
      evictionPolicy: 'least-recently-used'
    },

    // File system persistence
    fileSystem: {
      baseDir: './data/conversations/',
      format: 'json',
      compression: 'gzip',
      backupInterval: 3600000,    // Backup every hour
      maxFileSize: '10MB'
    },

    // Database persistence (future enhancement)
    database: {
      enabled: false,
      provider: 'postgresql',     // Future: PostgreSQL/MongoDB
      connectionString: process.env.DATABASE_URL,
      tableName: 'conversations'
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Conversation State Schema                                       │
  // └─────────────────────────────────────────────────────────────────┘
  conversationSchema: {
    id: 'uuid',                   // Unique conversation identifier
    sessionId: 'string',          // Browser session correlation
    userId: 'string|null',        // User identifier (if available)
    created: 'timestamp',         // Creation timestamp
    lastActivity: 'timestamp',    // Last interaction timestamp
    title: 'string',             // Auto-generated conversation title
    
    // Message history with metadata
    messages: [{
      id: 'uuid',
      role: 'user|assistant|system',
      content: 'string',
      timestamp: 'timestamp',
      provider: 'string',         // Which LLM provider was used
      model: 'string',           // Specific model used
      tokens: {
        prompt: 'number',
        completion: 'number',
        total: 'number'
      },
      processingTime: 'number',   // Response generation time
      sources: ['array']          // Graph sources used for context
    }],

    // Conversation metadata
    metadata: {
      totalMessages: 'number',
      totalTokens: 'number',
      averageResponseTime: 'number',
      primaryProvider: 'string',
      graphContext: {
        nodesReferenced: ['array'],
        relationshipsExplored: ['array']
      },
      preferences: {
        preferredProvider: 'string',
        temperature: 'number',
        maxTokens: 'number'
      }
    },

    // Conversation state flags
    state: {
      active: 'boolean',
      archived: 'boolean',
      pinned: 'boolean',
      shared: 'boolean'
    }
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Advanced Conversation Operations                                │
  // └─────────────────────────────────────────────────────────────────┘
  async createConversation(sessionId, initialMessage = null) {
    const conversation = {
      id: this.generateUUID(),
      sessionId,
      userId: null,
      created: new Date(),
      lastActivity: new Date(),
      title: initialMessage ? this.generateTitle(initialMessage) : 'New Conversation',
      messages: initialMessage ? [initialMessage] : [],
      metadata: this.initializeMetadata(),
      state: { active: true, archived: false, pinned: false, shared: false }
    };

    // Save to all persistence layers
    await this.saveConversation(conversation);
    return conversation;
  },

  async appendMessage(conversationId, message) {
    const conversation = await this.getConversation(conversationId);
    if (!conversation) throw new Error('Conversation not found');

    // Add message with full metadata
    const enrichedMessage = {
      ...message,
      id: this.generateUUID(),
      timestamp: new Date()
    };

    conversation.messages.push(enrichedMessage);
    conversation.lastActivity = new Date();
    conversation.metadata = this.updateMetadata(conversation.metadata, enrichedMessage);

    // Auto-generate title if this is early in conversation
    if (conversation.messages.length <= 3 && message.role === 'user') {
      conversation.title = this.generateTitle(message.content);
    }

    await this.saveConversation(conversation);
    return conversation;
  },

  async searchConversations(sessionId, query, options = {}) {
    const conversations = await this.getConversationsBySession(sessionId);
    
    return conversations.filter(conv => {
      // Search in title
      if (conv.title.toLowerCase().includes(query.toLowerCase())) return true;
      
      // Search in message content
      return conv.messages.some(msg => 
        msg.content.toLowerCase().includes(query.toLowerCase())
      );
    }).slice(0, options.limit || 20);
  }
};
```

## ⚡ Performance Optimization & Monitoring

### Request Processing Pipeline
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ FastAPI Request Processing Pipeline with Performance Monitoring            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 1. Request Reception & Validation                                           │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Receive HTTP request from proxy server                        │   │
│    │ • Validate request schema (Pydantic models)                     │   │
│    │ • Extract session ID and correlation headers                    │   │
│    │ • Start performance timer                                       │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 2. Session & Context Management                                             │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Load or create conversation session                           │   │
│    │ • Retrieve conversation history from persistence layer          │   │
│    │ • Build graph context from Neo4j knowledge base                │   │
│    │ • Optimize context for selected provider                       │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 3. Provider Selection & Health Check                                       │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Analyze user preference and system intelligence               │   │
│    │ • Check provider health and availability                        │   │
│    │ • Select optimal provider based on current conditions           │   │
│    │ • Prepare provider-specific request format                      │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 4. LLM Request Processing                                                   │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Send request to selected LLM provider                         │   │
│    │ • Handle streaming responses (if supported)                     │   │
│    │ • Monitor request progress and timeouts                         │   │
│    │ • Apply fallback if provider fails                              │   │
│    └──────────────────────────────────────────────────────────────────┘   │
│                                    │                                       │
│                                    ▼                                       │
│ 5. Response Processing & Persistence                                       │
│    ┌──────────────────────────────────────────────────────────────────┐   │
│    │ • Process and format LLM response                               │   │
│    │ • Extract citations and source references                       │   │
│    │ • Update conversation history with new messages                 │   │
│    │ • Record performance metrics and provider usage                 │   │
│    │ • Return structured response to client                          │   │
│    └──────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Performance Metrics & Monitoring
```python
# Comprehensive performance monitoring system
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            # Request-level metrics
            'request_duration': Histogram('request_duration_seconds'),
            'request_count': Counter('requests_total'),
            'error_count': Counter('errors_total'),
            
            # Provider-specific metrics
            'provider_response_time': Histogram('provider_response_time_seconds'),
            'provider_requests': Counter('provider_requests_total'),
            'provider_errors': Counter('provider_errors_total'),
            'provider_fallbacks': Counter('provider_fallbacks_total'),
            
            # System resource metrics
            'memory_usage': Gauge('memory_usage_bytes'),
            'cpu_usage': Gauge('cpu_usage_percent'),
            'active_conversations': Gauge('active_conversations'),
            
            # Context management metrics
            'context_window_utilization': Histogram('context_window_utilization_ratio'),
            'context_compression_ratio': Histogram('context_compression_ratio'),
            'token_usage': Counter('tokens_used_total')
        }
    
    def track_request(self, endpoint: str, method: str, duration: float, status_code: int):
        self.metrics['request_duration'].labels(
            endpoint=endpoint, 
            method=method, 
            status=status_code
        ).observe(duration)
        
        self.metrics['request_count'].labels(
            endpoint=endpoint, 
            method=method, 
            status=status_code
        ).inc()
        
        if status_code >= 400:
            self.metrics['error_count'].labels(
                endpoint=endpoint,
                error_type=self.classify_error(status_code)
            ).inc()
    
    def track_provider_usage(self, provider: str, model: str, response_time: float, 
                           tokens_used: int, success: bool):
        self.metrics['provider_response_time'].labels(
            provider=provider, 
            model=model
        ).observe(response_time)
        
        self.metrics['provider_requests'].labels(
            provider=provider, 
            model=model, 
            success=success
        ).inc()
        
        if not success:
            self.metrics['provider_errors'].labels(
                provider=provider, 
                model=model
            ).inc()
        
        self.metrics['token_usage'].labels(
            provider=provider, 
            model=model, 
            type='total'
        ).inc(tokens_used)
    
    def track_system_resources(self):
        import psutil
        import os
        
        # Memory usage
        process = psutil.Process(os.getpid())
        self.metrics['memory_usage'].set(process.memory_info().rss)
        
        # CPU usage  
        self.metrics['cpu_usage'].set(process.cpu_percent())
        
        # Active conversations (from conversation manager)
        active_count = len(conversation_manager.get_active_conversations())
        self.metrics['active_conversations'].set(active_count)
```

## 🧪 Testing & Quality Assurance

### Comprehensive Test Strategy
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Unit Tests          │ Integration Tests   │ E2E Tests           │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Provider Tests    │ • Multi-Provider    │ • Complete Chat     │
│ • Context Mgmt      │ • Failover Logic    │ • Provider Switch   │
│ • Error Handling    │ • Neo4j Integration │ • Error Recovery    │
│ • Conversation Mgmt │ • Performance       │ • Load Testing      │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Test Execution Matrix
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ AI Layer Testing Commands                                       │
# └─────────────────────────────────────────────────────────────────┘
python -m pytest tests/                     # All tests
python -m pytest tests/test_providers.py    # Provider tests
python -m pytest tests/test_failover.py     # Failover tests  
python -m pytest tests/test_context.py      # Context management
python -m pytest tests/test_conversation.py # Conversation tests

# ┌─────────────────────────────────────────────────────────────────┐
# │ Performance & Load Testing                                      │
# └─────────────────────────────────────────────────────────────────┘
python -m pytest tests/test_performance.py  # Performance benchmarks
python -m pytest tests/test_load.py         # Load testing
python -m pytest tests/test_memory.py       # Memory leak detection

# ┌─────────────────────────────────────────────────────────────────┐
# │ Provider-Specific Testing                                       │
# └─────────────────────────────────────────────────────────────────┘
python -m pytest -k "openai"                # OpenAI tests
python -m pytest -k "google"                # Google Gemini tests
python -m pytest -k "ollama"                # Ollama tests
python -m pytest -k "azure"                 # Azure OpenAI tests
```

### Mock Provider Testing
```python
# Advanced mock provider for testing
class MockLLMProvider:
    def __init__(self, name: str, failure_rate: float = 0.0, 
                 response_delay: float = 1.0):
        self.name = name
        self.failure_rate = failure_rate
        self.response_delay = response_delay
        self.request_count = 0
        self.error_count = 0
    
    async def generate_response(self, messages: List[dict], **kwargs) -> dict:
        self.request_count += 1
        
        # Simulate processing delay
        await asyncio.sleep(self.response_delay)
        
        # Simulate failures based on failure rate
        if random.random() < self.failure_rate:
            self.error_count += 1
            raise ProviderError(f"Mock failure from {self.name}")
        
        return {
            'response': f"Mock response from {self.name}",
            'provider': self.name,
            'model': 'mock-model',
            'tokens': {'prompt': 100, 'completion': 50, 'total': 150},
            'processing_time': self.response_delay
        }
    
    def health_check(self) -> dict:
        return {
            'status': 'healthy' if self.failure_rate < 0.5 else 'unhealthy',
            'requests': self.request_count,
            'errors': self.error_count,
            'error_rate': self.error_count / max(1, self.request_count)
        }
```

## 🚀 Installation & Configuration

### Quick Start Installation
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ AI Layer Setup                                                  │
# └─────────────────────────────────────────────────────────────────┘
# Navigate to project root
cd KnowledgeGraphVisualizer

# Install Python dependencies
pip install -r requirements.txt

# Copy environment template  
cp .env.example .env

# Configure LLM provider API keys (see Environment section)

# Start FastAPI server
python main.py                           # Production server (port 8000)
# OR
uvicorn main:app --reload                # Development with auto-reload
```

### Environment Configuration
```env
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# LLM PROVIDER CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# OpenAI Configuration (Primary)
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4-turbo
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# Google Gemini Configuration (Secondary)
GOOGLE_API_KEY=your-google-genai-key
GOOGLE_MODEL=gemini-pro
GOOGLE_MAX_TOKENS=4000
GOOGLE_TEMPERATURE=0.7

# Ollama Local Configuration (Tertiary)
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_MAX_TOKENS=2000
OLLAMA_TEMPERATURE=0.7

# Azure OpenAI Configuration (Enterprise)
AZURE_OPENAI_API_KEY=your-azure-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your-deployment-name
AZURE_OPENAI_VERSION=2023-12-01-preview

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PROVIDER SELECTION & FAILOVER
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
DEFAULT_PROVIDER=openai
PROVIDER_FALLBACK_ORDER=openai,google,ollama,azure
ENABLE_PROVIDER_FAILOVER=true
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_TIMEOUT=300

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# CONTEXT & CONVERSATION MANAGEMENT
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
MAX_CONVERSATION_HISTORY=50
CONTEXT_COMPRESSION_ENABLED=true
CONTEXT_COMPRESSION_RATIO=0.3
AUTO_TITLE_GENERATION=true
CONVERSATION_TTL=86400

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PERFORMANCE & MONITORING
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
ENABLE_PERFORMANCE_MONITORING=true
REQUEST_TIMEOUT=60
PROVIDER_HEALTH_CHECK_INTERVAL=60
LOG_LEVEL=INFO
METRICS_PORT=8001
```

## 🔧 API Reference

### Chat Endpoints
```
┌────────────────────────────────────────────────────────────────────────┐
│ Chat & Messaging Endpoints                                             │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint           │ Description                               │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ POST   │ /chat              │ Send chat message with context           │
│        │                    │ • Multi-provider LLM processing          │
│        │                    │ • Automatic provider failover            │
│        │                    │ • Graph context integration              │
│        │                    │ • Conversation history management        │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ POST   │ /chat/stream       │ Real-time streaming chat                 │
│        │                    │ • Server-sent events (SSE)               │
│        │                    │ • Token-by-token streaming               │
│        │                    │ • Real-time response display             │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ GET    │ /conversations     │ List user conversations                  │
│        │                    │ • Session-based filtering                │
│        │                    │ • Pagination support                     │
│        │                    │ • Search and sorting                     │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ POST   │ /conversations     │ Create new conversation                  │
│        │                    │ • Initialize conversation session        │
│        │                    │ • Auto-generate conversation title       │
│        │                    │ • Set initial context                    │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ GET    │ /conversations/:id │ Get specific conversation                │
│        │                    │ • Full message history                   │
│        │                    │ • Conversation metadata                  │
│        │                    │ • Performance statistics                 │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ DELETE │ /conversations/:id │ Delete conversation                      │
│        │                    │ • Soft delete with archive option        │
│        │                    │ • Cleanup conversation resources         │
│        │                    │ • Update session state                   │
└────────┴────────────────────┴───────────────────────────────────────────┘
```

### System Management Endpoints
```
┌────────────────────────────────────────────────────────────────────────┐
│ System Health & Management Endpoints                                  │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint           │ Description                               │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ GET    │ /health            │ Comprehensive health check               │
│        │                    │ • All provider status                    │
│        │                    │ • System resource usage                  │
│        │                    │ • Database connectivity                  │
│        │                    │ • Performance metrics                    │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ GET    │ /providers         │ List available LLM providers             │
│        │                    │ • Provider capabilities                  │
│        │                    │ • Health status                          │
│        │                    │ • Current usage statistics               │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ GET    │ /providers/health  │ Detailed provider health                 │
│        │                    │ • Individual provider status             │
│        │                    │ • Response time metrics                  │
│        │                    │ • Error rates and availability           │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ GET    │ /metrics           │ Prometheus metrics endpoint              │
│        │                    │ • Request/response metrics               │
│        │                    │ • Provider usage statistics              │
│        │                    │ • System resource metrics                │
├────────┼────────────────────┼───────────────────────────────────────────┤
│ POST   │ /debug/context     │ Debug context building                   │
│        │                    │ • Test context optimization              │
│        │                    │ • Token usage analysis                   │
│        │                    │ • Provider selection simulation          │
└────────┴────────────────────┴───────────────────────────────────────────┘
```

## 📊 Request/Response Examples

### Chat Request Example
```json
POST /chat
{
  "message": "What are the main dependencies of the Trading Engine?",
  "conversation_id": "conv_12345",
  "session_id": "sess_67890",
  "provider_preference": "openai",
  "include_graph_context": true,
  "stream": false,
  "settings": {
    "temperature": 0.7,
    "max_tokens": 1000
  }
}
```

### Chat Response Example
```json
{
  "response": "## 🔍 Trading Engine Dependencies\n\nBased on the knowledge graph analysis...",
  "conversation_id": "conv_12345",
  "message_id": "msg_98765",
  "provider_used": "openai",
  "model_used": "gpt-4-turbo",
  "processing_time_ms": 2340,
  "token_usage": {
    "prompt_tokens": 1250,
    "completion_tokens": 450,
    "total_tokens": 1700
  },
  "sources": [
    {
      "node_id": "trading_engine_001",
      "name": "Trading Engine Core",
      "type": "Module",
      "relevance_score": 0.95
    }
  ],
  "metadata": {
    "context_window_utilization": 0.68,
    "graph_nodes_referenced": 15,
    "fallback_used": false,
    "timestamp": "2025-01-15T14:30:22Z"
  }
}
```

### Health Check Response Example
```json
GET /health
{
  "status": "healthy",
  "timestamp": "2025-01-15T14:30:22Z",
  "uptime_seconds": 86400,
  "version": "2.0.0",
  "providers": {
    "openai": {
      "status": "healthy",
      "response_time_avg_ms": 1200,
      "success_rate": 0.98,
      "last_error": null
    },
    "google": {
      "status": "healthy", 
      "response_time_avg_ms": 800,
      "success_rate": 0.97,
      "last_error": null
    },
    "ollama": {
      "status": "degraded",
      "response_time_avg_ms": 3500,
      "success_rate": 0.85,
      "last_error": "Connection timeout"
    }
  },
  "system": {
    "memory_usage_mb": 245,
    "cpu_usage_percent": 15,
    "active_conversations": 23,
    "total_requests": 15420
  },
  "database": {
    "neo4j_status": "connected",
    "connection_pool_active": 5,
    "connection_pool_idle": 45
  }
}
```

## 🤝 Contributing & Development

### Development Setup
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development Environment                                         │
# └─────────────────────────────────────────────────────────────────┘
# Create virtual environment
python -m venv ai_env
source ai_env/bin/activate  # Linux/macOS
# OR
ai_env\Scripts\activate     # Windows

# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Start development server with auto-reload
uvicorn main:app --reload --port 8000
```

### Code Quality Standards
- **Type Hints**: All functions must have proper type annotations
- **Docstrings**: Comprehensive Google-style docstrings required
- **Testing**: 85%+ test coverage for new code
- **Error Handling**: Proper exception handling with meaningful messages

### Architecture Principles
1. **Provider Abstraction**: All LLM providers behind unified interface
2. **Graceful Degradation**: System continues operating with reduced providers  
3. **Performance First**: Async operations, connection pooling, caching
4. **Observability**: Comprehensive metrics, logging, and health checks
5. **Scalability**: Stateless design enabling horizontal scaling

---

**Last Updated**: January 2025 | **Version**: 2.0.0 | **Architecture**: FastAPI + Multi-Provider LLM + Advanced Failover