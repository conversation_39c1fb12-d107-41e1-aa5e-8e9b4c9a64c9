/**
 * Comprehensive Playwright Tests for Streaming Thinking Content Display
 * 
 * These tests verify that thinking content is visible during streaming
 * and properly preserved after streaming completes.
 */

import { test, expect } from '@playwright/test';

test.describe('Streaming Thinking Content Display', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the chat interface
    await page.goto('http://localhost:5177/');
    await page.waitForLoadState('networkidle');
    
    // Wait for the chat interface to be ready
    await expect(page.locator('.chat-view-modern')).toBeVisible();
  });

  test('should display thinking content during streaming', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();
    await expect(streamToggle).toBeChecked();

    // Type a test question that will trigger thinking
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('What is a Prime Broker and how does it work?');

    // Submit the question
    await page.keyboard.press('Enter');

    // Wait for the assistant message to appear
    await expect(page.locator('.message-bubble.assistant')).toBeVisible({ timeout: 30000 });

    // Check if thinking content is displayed during streaming
    const thinkingPanel = page.locator('.thinking-stream');
    await expect(thinkingPanel).toBeVisible({ timeout: 15000 });

    // Verify thinking content contains actual content (not just "Processing...")
    const thinkingContent = await thinkingPanel.textContent();
    expect(thinkingContent).toBeTruthy();
    expect(thinkingContent.length).toBeGreaterThan(10);

    // Wait for streaming to complete
    await expect(page.locator('.streaming-indicator')).not.toBeVisible({ timeout: 60000 });

    // Verify structured response is displayed after streaming
    await expect(page.locator('.assistant-response')).toBeVisible();
  });

  test('should preserve thinking content after streaming completes', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    // Send a question
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('How are daily settlement limits applied?');
    await page.keyboard.press('Enter');

    // Wait for streaming to complete
    await expect(page.locator('.message-bubble.assistant')).toBeVisible({ timeout: 30000 });
    await expect(page.locator('.streaming-indicator')).not.toBeVisible({ timeout: 60000 });

    // Check if thinking content is preserved in collapsible section
    const thinkingDetails = page.locator('.thinking-details');
    if (await thinkingDetails.count() > 0) {
      // If collapsible thinking section exists, verify it's functional
      await expect(thinkingDetails).toBeVisible();
      
      // Click to expand thinking content
      const thinkingSummary = page.locator('.thinking-summary');
      await thinkingSummary.click();
      
      // Verify thinking content is displayed
      const thinkingText = page.locator('.thinking-text');
      await expect(thinkingText).toBeVisible();
      
      const preservedContent = await thinkingText.textContent();
      expect(preservedContent).toBeTruthy();
      expect(preservedContent.length).toBeGreaterThan(5);
    }
  });

  test('should show real-time thinking updates during streaming', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    // Monitor console logs for debugging output
    const consoleLogs = [];
    page.on('console', msg => {
      if (msg.text().includes('🧠') || msg.text().includes('🤔') || msg.text().includes('🎨')) {
        consoleLogs.push(msg.text());
      }
    });

    // Send a question
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('What is the difference between gross and net settlement?');
    await page.keyboard.press('Enter');

    // Wait for thinking panel to appear
    const thinkingPanel = page.locator('.thinking-stream');
    await expect(thinkingPanel).toBeVisible({ timeout: 15000 });

    // Monitor thinking content updates for at least 5 seconds
    let previousContent = '';
    let contentUpdates = 0;
    
    for (let i = 0; i < 10; i++) {
      await page.waitForTimeout(500);
      
      if (await thinkingPanel.count() > 0) {
        const currentContent = await thinkingPanel.textContent();
        if (currentContent !== previousContent && currentContent.length > previousContent.length) {
          contentUpdates++;
          previousContent = currentContent;
        }
      }
    }

    // Verify that thinking content was updated in real-time
    expect(contentUpdates).toBeGreaterThan(0);

    // Verify debugging logs were generated
    expect(consoleLogs.length).toBeGreaterThan(0);
    
    // Wait for streaming to complete
    await expect(page.locator('.streaming-indicator')).not.toBeVisible({ timeout: 60000 });
  });

  test('should handle thinking content edge cases gracefully', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    // Test with a very short question that might produce minimal thinking
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('Yes');
    await page.keyboard.press('Enter');

    // Wait for response
    await expect(page.locator('.message-bubble.assistant')).toBeVisible({ timeout: 30000 });

    // Verify no errors occurred (page should still be functional)
    await expect(page.locator('.chat-error-modern')).not.toBeVisible();
    
    // Verify UI is still responsive
    await inputField.fill('Test follow-up question');
    await expect(inputField).toHaveValue('Test follow-up question');
  });

  test('should display thinking content with proper styling', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    // Send a question
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('Explain the concept of risk-reversal strategy');
    await page.keyboard.press('Enter');

    // Wait for thinking panel
    const thinkingPanel = page.locator('.thinking-stream');
    await expect(thinkingPanel).toBeVisible({ timeout: 15000 });

    // Verify CSS styling is applied
    const styles = await thinkingPanel.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        backgroundColor: computed.backgroundColor,
        border: computed.border,
        borderRadius: computed.borderRadius,
        fontFamily: computed.fontFamily,
        whiteSpace: computed.whiteSpace
      };
    });

    // Verify essential styles are applied
    expect(styles.whiteSpace).toBe('pre-wrap');
    expect(styles.borderRadius).toBe('8px');
    expect(styles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)'); // Not transparent
  });

  test('should work consistently across multiple questions', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    const testQuestions = [
      'What is a Prime Broker?',
      'How are settlement limits calculated?',
      'Explain the trading workflow process'
    ];

    for (const question of testQuestions) {
      // Send question
      const inputField = page.locator('.chat-input-modern');
      await inputField.fill(question);
      await page.keyboard.press('Enter');

      // Wait for thinking panel to appear
      const thinkingPanel = page.locator('.thinking-stream');
      await expect(thinkingPanel).toBeVisible({ timeout: 15000 });

      // Wait for streaming to complete
      await expect(page.locator('.streaming-indicator')).not.toBeVisible({ timeout: 60000 });

      // Verify structured response is displayed
      await expect(page.locator('.assistant-response')).toBeVisible();

      // Small delay between questions
      await page.waitForTimeout(1000);
    }

    // Verify all messages are displayed
    const allMessages = page.locator('.message-bubble');
    expect(await allMessages.count()).toBeGreaterThanOrEqual(6); // 3 user + 3 assistant messages
  });

  test('should handle network interruptions gracefully', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    // Start a question
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('What are the components of the 360T platform?');
    await page.keyboard.press('Enter');

    // Wait briefly for streaming to start
    await page.waitForTimeout(2000);

    // Simulate network issue by going offline
    await page.context().setOffline(true);

    // Wait a moment
    await page.waitForTimeout(1000);

    // Go back online
    await page.context().setOffline(false);

    // Verify the chat interface recovers gracefully
    await expect(page.locator('.chat-view-modern')).toBeVisible();
    
    // Verify no persistent error states
    await page.waitForTimeout(2000);
    const errorElements = page.locator('.chat-error-modern');
    if (await errorElements.count() > 0) {
      // If error is shown, it should be clearable
      expect(await errorElements.textContent()).toBeTruthy();
    }
  });

  test('should take screenshot for visual regression testing', async ({ page }) => {
    // Enable streaming mode
    const streamToggle = page.locator('.streaming-toggle-modern input[type="checkbox"]');
    await streamToggle.check();

    // Send a question
    const inputField = page.locator('.chat-input-modern');
    await inputField.fill('Show me information about market making strategies');
    await page.keyboard.press('Enter');

    // Wait for thinking panel
    const thinkingPanel = page.locator('.thinking-stream');
    await expect(thinkingPanel).toBeVisible({ timeout: 15000 });

    // Take screenshot during streaming with thinking content visible
    await page.screenshot({ 
      path: 'test-results/streaming-thinking-active.png',
      fullPage: true 
    });

    // Wait for streaming to complete
    await expect(page.locator('.streaming-indicator')).not.toBeVisible({ timeout: 60000 });

    // Wait for structured response to fully load
    await expect(page.locator('.assistant-response')).toBeVisible();
    await page.waitForTimeout(2000);

    // Take screenshot after streaming completes
    await page.screenshot({ 
      path: 'test-results/streaming-thinking-completed.png',
      fullPage: true 
    });
  });
});